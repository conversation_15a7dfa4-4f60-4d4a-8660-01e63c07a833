<!-- 登陆中转页 -->
<template>
    <div>

    </div>
</template>

<script>
import { mapActions } from 'vuex'
import { INDEX_MAIN_PAGE_PATH } from '@base/store/mutation-types'
import loginMixin from '@/mixins/loginMixin'
import * as tokenUtil from "@base/utils/base/token-util";
import * as userStoreUtil from "@base/utils/base/user-store-util";
export default {
    mixins: [loginMixin],
    data() {
        return {
            load: null,
            loginForm: {}
        }
    },
    created() {
        this.loading()
        if(this.$route.query.forHis){
            this.loginForHis()
        }else{
            this.loginByToken()
        }
    },
    methods: {
        ...mapActions(['Login']),
        loading() {
            this.load = this.$loading({
                // lock: true,
                fullscreen: true,
                text: 'Loading',
                spinner: 'hos-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)',
                text: '登陆中，请稍候...'
            });
        },
        async loginForHis(){
            this.$api('biz.index.loginByToken', {
                grantType: 'his',
                accountId: this.$route.query.accountId,
                aesString: this.$route.query.aesString,
            }).then(res => {
                if (res.code == 200) {
                    this.loginSuccessHandler(res)
                    this.load.close()
                } else {
                    this.$message({
                        message:res.msg,
                        type:'error'
                    })
                    // setTimeout(() => {
                    //     this.$router.replace({ path: '/login/login', query:{redirect:this.$route.redirect} })
                    // }, 1500)
                }
            }).catch((err) => {
                console.trace(err)
                if (!err.code || !err.code.includes('101-002-005-')) {
                    if(err.msg){
                        this.$message({
                            message:err.msg,
                            type:'error'
                        })
                    }
                }
                // 回到登陆页面
                // setTimeout(() => {
                //     this.$router.replace({ path: '/login/login', query:{redirect:this.$route.redirect} })
                // }, 3000)
            }).finally(()=>{
                this.load.close()
            })
        },
        async loginByToken() {
            if (!localStorage.getItem('loginType')) {
                const { code, data } = await this.$api('biz.index.getLoginModel')
                if (code == 200) {
                    localStorage.setItem('loginType', JSON.stringify(data))
                }
            }
            const urlParams = this.getUrlParamsInUrl(location.href)
            const token = urlParams['token']
            const loginType = JSON.parse(localStorage.getItem('loginType'))
            // if(!token) {
            //     location.replace(loginType.redirectUrl)
            // }
            if(!loginType || !loginType.isOtherSystem) {
                this.$message.warning(this.$t('未开启第三方登陆配置，联系管理人员'))
                setTimeout(() => {
                    location.replace(loginType.redirectUrl)
                }, 3000)
            }

            this.$api('biz.index.loginByToken', {
                accessToken: token,
                grantType: loginType.model,
                ...urlParams
            }).then(res => {
                if (res.code == 200) {
                    this.loginSuccessHandler(res)
                    this.load.close()
                } else {
                    this.$message.error(res.msg)
                    setTimeout(() => {
                        location.replace(loginType.redirectUrl)
                    }, 1500)
                }
            }).catch((err) => {
                if (!err.code || !err.code.includes('101-002-005-')) {
                    this.$message.error(err.msg)
                }
                // 回到登陆页面
                setTimeout(() => {
                    location.replace(loginType.redirectUrl)
                }, 3000)
            })
        },
        loginSuccessHandler(response) {
            const result = response.data;
            ///用户基础信息
            const hosUser = result.hosUser || {};
            ///用户扩展信息
            const detail = result.detail || {};
            ///将用户信息合并成userinfo保存
            const userInfo = Object.assign(hosUser, detail);
            //全部封装到用户信息中放到localstorage中
            tokenUtil.setToken(result.accessToken);
            tokenUtil.setRefreshToken(result.refreshToken);
            // 保存登录来源
            this.$store.commit('SET_LOGINTYPE', result.type)
            sessionStorage.setItem('loginType', result.type)
            ///其他信息直接使用缓存类去保存
            userStoreUtil.setTenantId(userInfo.tenantId);
            userStoreUtil.setLoginName(userInfo.loginName);
            userStoreUtil.setUserInfo(userInfo);
            userStoreUtil.setAvatar(userInfo.avatar);
            userStoreUtil.setPolicyErrorCode(userInfo.policyErrorCode);
            userStoreUtil.setPostId(userInfo.postId);


            this.$store.commit("SET_ACCESS_TOKEN", result.accessToken);
            this.$store.commit("SET_REFRESH_TOKEN", result.refreshToken);
            this.$store.commit("SET_USER_INFO", userInfo); //用户信息
            // 存储postId 岗位id
            this.$store.commit("SET_POST_ID", userInfo.postId);

            ///后用户名、姓名
            this.$store.commit("SET_NAME", {
                loginName: userInfo.loginName,
                name: userInfo.name,
            });
            this.$store.commit("SET_AVATAR", userInfo.avatar); ///头像
            this.$store.commit("SET_TENANT", userInfo.tenantId); ////租户id
            this.$store.commit("SET_DEFAULT_PAGE_DATA", userInfo.defaultPageData); ///默认显示的页面
            this.$store.commit("SET_IS_SELECT_ROLE", userInfo.isSelectRole);
            this.$store.commit("SET_POLICY_ERROR_CODE", userInfo.policyErrorCode);


            let toCustomPath
            if (this.$route.query && this.$route.query.redirect) {
                toCustomPath = this.$route.query.redirect
            } else {
                toCustomPath = INDEX_MAIN_PAGE_PATH
            }
            // 登录成功后的自定义逻辑
            toCustomPath = this.mixinsLoginSucessHandler(toCustomPath)
            this.load.close()
            // 重定向到指定路径
            console.log('toCustomPath',toCustomPath)
            this.$router.replace(toCustomPath)
        },
        getUrlParamsInUrl(url) {
            // 获取URL中的参数
            const output = {};

            // 检查是否包含查询参数
            const queryStart = url.indexOf("?");
            if (queryStart === -1) {
                return output; // 如果没有查询参数，返回空对象
            }

            const queryString = url.slice(queryStart + 1);
            const params = queryString.split("&");

            params.forEach(param => {
                const [key, value] = param.split("=");
                const decodedKey = decodeURIComponent(key);
                const decodedValue = decodeURIComponent(value || "");

                if (decodedKey.search(/token/i) > -1) {
                    output["token"] = decodedValue;
                }
                output[decodedKey] = decodedValue;
            });

            return output;
        }
    }
}
</script>