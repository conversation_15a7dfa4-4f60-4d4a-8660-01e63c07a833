package com.mediway.platform.controller;


import com.mediway.hos.base.model.BaseResponse;
import com.mediway.hos.security.core.authentication.HosTokenAuthenticationToken;
import com.mediway.hos.security.core.userdetails.HosUser;
import com.mediway.hos.security.core.userdetails.HosUserDetails;
import com.mediway.hos.security.login.store.HosTokenStore;
import com.mediway.platform.commons.config.CustomLoginConfig;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 登录认证相关自定义接口服务
 *
 * <AUTHOR>
 * @date 2024/7/3 14:32
 */
@RestController
@RequestMapping("/openApi/hos/security")
public class OpenApiCsmSecurityController {

//    @Resource
//    private HosTokenStore hosTokenStore;

    @Resource
    private CustomLoginConfig customLoginConfig;

//    @ApiOperation("通过token获取用户code")
//    @GetMapping("/get-user-by-token")
//    public BaseResponse getUserByToken(@RequestParam String token) {
//        if (StringUtils.isBlank(token)) {
//            return BaseResponse.success();
//        }
//        HosTokenAuthenticationToken authenticationToken = hosTokenStore.getByToken(token);
//        if (authenticationToken == null || authenticationToken.getPrincipal() == null) {
//            return BaseResponse.success();
//        }
//        if (authenticationToken.getPrincipal() instanceof HosUserDetails) {
//            HosUser hosUser = ((HosUserDetails) authenticationToken.getPrincipal()).getHosUser();
//            return BaseResponse.success(hosUser.getAccountId());
//        }
//
//        return BaseResponse.success();
//    }


    @ApiOperation("获取当前系统登录模式信息")
    @GetMapping("/get-login-model")
    public BaseResponse getLoginModel() {
        //获取系统的登录模式，返回模式，重定向地址，是否是外部系统，是外部系统则开始界面是三方系统界面
        Map<String, Object> map = new HashMap<>();
        map.put("model", customLoginConfig.getModel());
        map.put("redirectUrl", customLoginConfig.getRedirectUrl());
        map.put("isOtherSystem", customLoginConfig.getIsOtherSystem());

        return BaseResponse.success(map);
    }
}


