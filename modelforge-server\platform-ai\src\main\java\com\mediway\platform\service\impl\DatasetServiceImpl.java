package com.mediway.platform.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mediway.hos.base.exception.SysExceptionEnum;
import com.mediway.hos.file.model.vo.FileVo;
import com.mediway.hos.file.model.vo.HosSysFileConfigVo;
import com.mediway.hos.file.service.FileStorageService;
import com.mediway.hos.file.service.HosSysFileConfigService;
import com.mediway.platform.async.FileProcessingAsync;
import com.mediway.platform.commons.config.CsmUserCacheHelper;
import com.mediway.platform.commons.exception.BusinessException;
import com.mediway.platform.commons.utils.FileUtils;
import com.mediway.platform.constant.Constant;
import com.mediway.platform.mapper.DatasetMapper;
import com.mediway.platform.mapper.DatasetVersionMapper;
import com.mediway.platform.mapper.DatasetFileContentMapper;
import com.mediway.platform.model.dto.DatasetDTO;
import com.mediway.platform.model.dto.FileInfoDTO;
import com.mediway.platform.model.entity.DatasetEntity;
import com.mediway.platform.model.entity.DatasetVersionEntity;
import com.mediway.platform.model.entity.DatasetFileContentEntity;
import com.mediway.platform.model.vo.DatasetVO;
import com.mediway.platform.service.DatasetDataService;
import com.mediway.platform.service.DatasetService;
import com.mediway.platform.service.DatasetFileContentService;
import com.mediway.platform.utils.*;
import com.opencsv.exceptions.CsvException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.Deflater;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 数据集服务层接口实现类，实现数据集管理的业务逻辑
 */
@Service
@Slf4j
public class DatasetServiceImpl extends ServiceImpl<DatasetMapper, DatasetEntity> implements DatasetService {

    @Resource
    private DatasetVersionMapper datasetVersionMapper;

    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private HosSysFileConfigService hosSysFileConfigService;

    @Resource
    private DatasetFileContentMapper fileContentMapper;

    @Resource
    private DatasetFileContentService fileContentService;

    @Resource
    private FileProcessingAsync fileProcessingAsync;

    @Resource
    private DatasetDataService datasetDataService;

    @Value("${modelforge.parquet.file-path}")
    private String parquetFilePathDir;

    @Value("${modelforge.python.base-url}")
    private String baseUrl;

    @Value("${modelforge.output.file-path}")
    private String outputFilePathDir;

    /**
     * 创建数据集
     * @param dto 数据集数据传输对象
     * @return 创建是否成功
     */
    @Override
    @Transactional
    public boolean createDataset(DatasetDTO dto) {
        DatasetEntity dataset = ConvertUtils.convertToEntity(dto, DatasetEntity.class);
        HosSysFileConfigVo activeFileConfig = hosSysFileConfigService.getActiveFileConfig();
        String path = activeFileConfig.getPath();
        dataset.setStorageDirectory(path);
        boolean save = this.save(dataset);
        if(save){ // 上传成功后，自动生成第一个 数据集版本
            DatasetVersionEntity versionEntity = new DatasetVersionEntity();
            versionEntity.setVersionNumber(VersionUtils.newVersion(null));
            versionEntity.setDatasetId(dataset.getId());
            versionEntity.setVersionDescription(Constant.BASE_VERSION_DESCRIPTION);
            versionEntity.setFileId(dto.getVersionFileId());
            String accountId = CsmUserCacheHelper.me().getLoginPersonUser().getAccountId();
            versionEntity.setOperator(accountId);
            int insert = datasetVersionMapper.insert(versionEntity);
            return insert > 0;
        }

        return false;
    }

    /**
     * 更新数据集
     * @param dto 数据集数据传输对象
     * @return 更新是否成功
     */
    @Override
    public boolean updateDataset(DatasetDTO dto) {
        DatasetEntity dataset = ConvertUtils.convertToEntity(dto, DatasetEntity.class);
        return this.saveOrUpdate(dataset);
    }

    /**
     * 删除数据集（逻辑删除，需根据业务需求调整）
     * @param id 数据集的 ID
     * @return 删除是否成功
     */
    @Override
    public boolean deleteDataset(String id) {
        LambdaQueryWrapper<DatasetVersionEntity> versionEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
        versionEntityLambdaQueryWrapper.eq(DatasetVersionEntity::getDatasetId,id);
        List<DatasetVersionEntity> versionEntities = datasetVersionMapper.selectList(versionEntityLambdaQueryWrapper);

        if (!versionEntities.isEmpty()) {
            throw new BusinessException("有关联 数据集版本，请先删除关联数据集版本");
        }

        return this.removeById(id);
    }

    /**
     * 获取数据集详情
     * @param id 数据集的 ID
     * @return 数据集视图对象，若不存在则返回 null
     */
    @Override
    public DatasetVO getDatasetById(String id) {
        DatasetEntity dataset = this.getById(id);
        if (dataset == null) {
            return null;
        }
        return ConvertUtils.convertToVO(dataset, DatasetVO.class);
    }

    /**
     * 获取所有数据集
     * @param type 数据集类型，可为空
     * @return 数据集视图对象列表
     */
    @Override
    public List<DatasetVO> getAllDatasets(String type) {
        LambdaQueryWrapper<DatasetEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (type != null && !type.isEmpty()) {
            queryWrapper.eq(DatasetEntity::getType, type);
        }
        List<DatasetEntity> datasets = this.list(queryWrapper);
        return datasets.stream()
                .map(entity -> {
                    DatasetVO vo = ConvertUtils.convertToVO(entity, DatasetVO.class);
                    String datasetId = vo.getId();
                    LambdaQueryWrapper<DatasetVersionEntity> versionQueryWrapper = new LambdaQueryWrapper<>();
                    versionQueryWrapper.eq(DatasetVersionEntity::getDatasetId, datasetId);
                    Long selectCount = datasetVersionMapper.selectCount(versionQueryWrapper);
                    vo.setVersionCount(Math.toIntExact(selectCount));
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 分页查询数据集
     * @param pageNum 当前页码
     * @param pageSize 每页记录数
     * @param name 数据集名称，可为空
     * @param type 数据集类型，可为空
     * @return 分页的数据集视图对象
     */
    @Override
    public IPage<DatasetVO> getDatasetPage(Integer pageNum, Integer pageSize, String name, Integer type, String dataUsage, Integer isActivity) {
        IPage<DatasetEntity> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<DatasetEntity> queryWrapper = new LambdaQueryWrapper<>();

        if (name != null && !name.isEmpty()) {
            queryWrapper.like(DatasetEntity::getName, name);
        }
        if (type != null) {
            queryWrapper.eq(DatasetEntity::getType, type);
        }
        if (dataUsage != null) {
            queryWrapper.eq(DatasetEntity::getDataUsage, dataUsage);
        }

        if (isActivity != null) {
            queryWrapper.eq(DatasetEntity::getIsActivity, isActivity);
        }
        IPage<DatasetEntity> entityPage = this.page(page, queryWrapper);
        IPage<DatasetVO> voPage = new Page<>(pageNum, pageSize);
        voPage.setTotal(entityPage.getTotal());

        List<DatasetVO> vos = entityPage.getRecords().stream()
                .map(entity -> {
                    DatasetVO vo = ConvertUtils.convertToVO(entity, DatasetVO.class);
                    String datasetId = vo.getId();
                    LambdaQueryWrapper<DatasetVersionEntity> versionQueryWrapper = new LambdaQueryWrapper<>();
                    versionQueryWrapper.eq(DatasetVersionEntity::getDatasetId, datasetId);
                    Long selectCount = datasetVersionMapper.selectCount(versionQueryWrapper);
                    vo.setVersionCount(Math.toIntExact(selectCount));
                    return vo;
                })
                .collect(Collectors.toList());
        voPage.setRecords(vos);

        return voPage;
    }

    /**
     * 批量删除数据集
     * @param ids 要删除的数据集 ID 列表
     * @return 批量删除是否成功
     */
    @Override
    public boolean batchDeleteDatasets(List<String> ids) {
        for(String id : ids){
            LambdaQueryWrapper<DatasetVersionEntity> versionEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
            versionEntityLambdaQueryWrapper.eq(DatasetVersionEntity::getDatasetId,id);
            List<DatasetVersionEntity> versionEntities = datasetVersionMapper.selectList(versionEntityLambdaQueryWrapper);

            if (!versionEntities.isEmpty()) {
                throw new BusinessException("有关联 数据集版本，请先删除关联数据集版本");
            }
        }
        return this.removeByIds(ids);
    }

    /**
     * 获取所有数据集 - 级联选择器数据
     * @param type 数据集类型，可为空
     * @return 级联选择器所需的数据结构
     */
    @Override
    public List<Map<String, Object>> getSpecificDataFromDB(String type) {
        List<Map<String, Object>> result = new ArrayList<>();

        LambdaQueryWrapper<DatasetEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (type != null && !type.isEmpty()) {
            queryWrapper.eq(DatasetEntity::getType, type);
        }
        //只查询启用的数据集
        queryWrapper.eq(DatasetEntity::getIsActivity, 1);
        List<DatasetEntity> mainCategories = this.list(queryWrapper);

        for (DatasetEntity mainCategory : mainCategories) {
            Map<String, Object> item = new HashMap<>();
            item.put("id", mainCategory.getId());
            item.put("name", mainCategory.getName());

            String mainCategoryId = mainCategory.getId();

            LambdaQueryWrapper<DatasetVersionEntity> versionQueryWrapper = new LambdaQueryWrapper<>();
            versionQueryWrapper.eq(DatasetVersionEntity::getDatasetId, mainCategoryId);
            //只返回启用的数据集版本
            versionQueryWrapper.eq(DatasetVersionEntity::getIsActivity, 1);

            List<DatasetVersionEntity> subCategories = datasetVersionMapper.selectList(versionQueryWrapper);

            List<Map<String, Object>> children = new ArrayList<>();
            for (DatasetVersionEntity subCategory : subCategories) {
                Map<String, Object> child = new HashMap<>();
                child.put("id", subCategory.getId());
                child.put("name", subCategory.getVersionNumber());
                children.add(child);
            }

            item.put("children", children);
            result.add(item);
        }

        return result;
    }

    @Override
    public IPage<Map<String, Object>> getFileInfo(String fileId, Integer current, Integer size, String search) {
        IPage<Map<String, Object>> page = new Page<>(current, size);
        try {
            String[] fileIds = fileId.split(",");
            List<Map<String, Object>> allList = new ArrayList<>();

            for(int i = 0; i < fileIds.length; i++){
                String fId = fileIds[i];
                // 读取文件内容
                LambdaQueryWrapper<DatasetFileContentEntity> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(DatasetFileContentEntity::getReferenceFileId, fId)
                        .last("limit " + Constant.INIT_FILE_LINE)
                        .orderByAsc(DatasetFileContentEntity::getLineNumber);  // 按照 line_number 升序排序
                List<DatasetFileContentEntity> fileContentEntitys = fileContentMapper.selectList(queryWrapper);
                if (fileContentEntitys.isEmpty()) {
                    continue;
                }

                List<Map<String, Object>> list = fileContentEntitys.stream()
                        .map(version -> ConvertUtils.convertToMap(version))
                        .collect(Collectors.toList());

                allList.addAll(list);
            }

            List<Map<String, Object>> result = DealFileContentUtils.readFileContentPage(allList,current,size,search);

            // 创建 IPage 对象
            page.setRecords(result);
            page.setTotal(allList.size());

            return page;

        } catch (IOException e) {
            e.printStackTrace();
        } catch (CsvException e) {
            e.printStackTrace();
        }
        return page;
    }


    @Override
    public Boolean versionImport(MultipartFile file, String versionId, String fileName, int chunk, int totalChunks) {

        // 根据 版本ID 获取 文件内容
        DatasetVersionEntity versionEntity = datasetVersionMapper.selectById(versionId);
        if (versionEntity == null) {
            return false;
        }
        String versionNumber = versionEntity.getVersionNumber();
        String datasetId = versionEntity.getDatasetId();

        datasetDataService.parseAndSaveDatasetData(datasetId, versionNumber,  file);

//        // 先上传文件
//        String fileId = null;
//        try {
//            String oldFid = oldFileId.split(",")[0];
//            String oldFilePath = FileFormatChecker.getFullFilePath(parquetFilePathDir) + File.separator + oldFid;
//            fileId = uploadFile(file,fileName,chunk,totalChunks,true, oldFilePath);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//
//        if (fileId == null){ // 如果返回的是其他分片，则不插入
//            return true;
//        }
//
//        String newReferenceFileId = oldFileId + "," + fileId;
//        versionEntity.setFileId(newReferenceFileId);
//
//        // 插入或更新到数据库中
//        int result = datasetVersionMapper.updateById(versionEntity);

        return true;
    }

    /**
     * 不读取文件内容，然后进行格式转化。
     * 直接将文件变成输入流。
     * @param versionId
     * @param fileType
     * @return
     */
    @Override
    public FileInfoDTO getContentAsFile(String versionId, Integer fileType) {
        // 参数校验
        if (versionId == null || fileType == null) {
            throw new BusinessException("versionId和fileType不能为空");
        }

        // 获取版本和数据集信息
        DatasetVersionEntity versionEntity = datasetVersionMapper.selectById(versionId);
        if (versionEntity == null) {
            throw new BusinessException("版本不存在，ID: " + versionId);
        }

        DatasetEntity datasetEntity = this.getById(versionEntity.getDatasetId());
        if (datasetEntity == null) {
            throw new BusinessException("数据集不存在，ID: " + versionEntity.getDatasetId());
        }

        // 解析文件ID列表
        String[] fileIds = StringUtils.split(versionEntity.getFileId(), ",");
        if (fileIds == null || fileIds.length == 0) {
            throw new BusinessException("版本没有关联的文件，ID: " + versionId);
        }

        String baseFileName = FileFormatChecker.sanitizeFileName(datasetEntity.getName()) + "_v" + versionEntity.getVersionNumber();
        String fileNameWithExtension = FileFormatChecker.addExtension(baseFileName, fileType);

        ByteArrayOutputStream zipOutputStream = new ByteArrayOutputStream();
        ZipOutputStream zip = new ZipOutputStream(zipOutputStream);

        try {
            // 设置压缩级别
            zip.setLevel(Deflater.DEFAULT_COMPRESSION);

            boolean hasValidFile = false;

            // 处理所有文件（之前的代码只处理了第一个文件）
            String fileId = fileIds[0];

            try {
                String filePath = FileFormatChecker.getFullFilePath(parquetFilePathDir) + File.separator + fileId;
                String outputPath = FileFormatChecker.getFullFilePath(outputFilePathDir) + File.separator + fileNameWithExtension;

                // 转换文件格式
                JSONObject result = ParquetApiUtil.convertAndSaveFile(FileFormatChecker.getParquetUrl(baseUrl,"parquet"), filePath, outputPath);

                if (result.containsKey("message")) {
                    String convertedFilePath = result.getStr("message"); // 修正为getString
                    // 将转换后的文件添加到ZIP
                    addFileToZip(zip, convertedFilePath, fileNameWithExtension);
                    hasValidFile = true;
                    log.info("文件转换成功，源文件: {}, 目标文件: {}", fileId, convertedFilePath);
                } else if (result.containsKey("error")) {
                    String errorMsg = result.getStr("error"); // 修正为getString
                    log.error("文件转换失败，ID: {}, 错误: {}", fileId, errorMsg);
                    throw new BusinessException("文件转换失败，ID: "+ fileId +", 错误: "+ errorMsg);
                } else {
                    log.error("文件转换返回未知格式，ID: {}, 返回: {}", fileId, result.toString());
                    throw new BusinessException("文件处理失败，未知返回格式");
                }

            } catch (BusinessException | IOException e) {
                // 记录错误并继续处理其他文件
                log.error("处理文件失败，ID: {}, 错误: {}", fileId, e.getMessage());
            } catch (Exception e) {
                // 记录系统异常并继续
                log.error("处理文件发生系统错误，ID: {}, 错误: {}", fileId, e.getMessage(), e);
            }

            // 确保ZIP文件有内容
            if (!hasValidFile) {
                throw new BusinessException("没有成功处理任何文件");
            }

        } catch (Exception e) {
            // 关闭资源并重新抛出异常
            closeQuietly(zip);
            closeQuietly(zipOutputStream);
            throw new BusinessException("生成ZIP文件失败: " + e.getMessage());
        } finally {
            // 确保资源被关闭
            closeQuietly(zip);
        }

        return new FileInfoDTO(zipOutputStream.toByteArray(), baseFileName + ".zip");
    }


    public FileInfoDTO getContentAsFileWithDatasetDb(String versionId, Integer fileType) {
        // 参数校验
        if (versionId == null || fileType == null) {
            throw new BusinessException("versionId和fileType不能为空");
        }

        // 获取版本和数据集信息
        DatasetVersionEntity versionEntity = datasetVersionMapper.selectById(versionId);
        if (versionEntity == null) {
            throw new BusinessException("版本不存在，ID: " + versionId);
        }

        DatasetEntity datasetEntity = this.getById(versionEntity.getDatasetId());
        if (datasetEntity == null) {
            throw new BusinessException("数据集不存在，ID: " + versionEntity.getDatasetId());
        }
        String datasetId = versionEntity.getDatasetId();
        String version = versionEntity.getVersionNumber();

        try {

            // 导出数据到字节流
            FileInfoDTO fileInfoDTO = datasetDataService.exportDatasetDataToFileInfo(datasetId, version);

            // 创建并返回FileInfoDTO
            return fileInfoDTO;

        } catch (Exception e) {
            log.error("导出数据集为FileInfoDTO失败", e);
            throw new RuntimeException("导出数据集失败", e);
        }
    }

    /**
     * 安静地关闭Closeable资源
     */
    private void closeQuietly(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                log.warn("关闭资源失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 将文件添加到ZIP流
     */
    private void addFileToZip(ZipOutputStream zip, String filePath, String entryName) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            ZipEntry zipEntry = new ZipEntry(entryName);
            zip.putNextEntry(zipEntry);

            byte[] bytes = new byte[1024];
            int length;
            while ((length = fis.read(bytes)) >= 0) {
                zip.write(bytes, 0, length);
            }

            zip.closeEntry();
        }
    }

    @Override
    public String uploadFile(MultipartFile file, String fileName, int chunk, int totalChunks, boolean isAppend, String appendPath) throws IOException {
        // 1.参数校验
        if (Objects.isNull(file) || file.isEmpty()) {
            throw new BusinessException(SysExceptionEnum.FILE_NOT_FOUND_EXCEPTION);
        }

        // 检查文件类型是否允许
        String fileExtension = DealFileContentUtils.getFileExtension(fileName);
        if (!Constant.ALLOWED_FILE_TYPES.contains(fileExtension)) {
            throw new BusinessException("不支持的文件类型，仅支持 " + String.join(", ", Constant.ALLOWED_FILE_TYPES));
        }

        HosSysFileConfigVo activeFileConfig = hosSysFileConfigService.getActiveFileConfig();
        String uploadDir = activeFileConfig.getPath();

        // 将文件保存为分片
        File chunkFile = new File(uploadDir + "/" + fileName + ".part" + chunk);
        try (InputStream inputStream = file.getInputStream();
             OutputStream outputStream = new FileOutputStream(chunkFile)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }

        // 如果是最后一个分片，合并所有分片
        if (chunk == totalChunks - 1) {
            // 使用 UUID 代替原来的文件名字
            String compactUuid = UUID.randomUUID().toString().replace("-", "");
            FileVo fileVo = mergeChunks(fileName, totalChunks, uploadDir);
            if (fileVo == null) {
                return null;
            }
            return fileVo.getId();
//            try {
//                // 先同步插入前10条数据，供页面展示
//                processUploadedFileInitLine(compactUuid,filePath);
//                // 异步生成 parquet 文件
//                String path = FileFormatChecker.getFullFilePath(parquetFilePathDir) + File.separator + compactUuid;
//                fileProcessingAsync.fileToParquet(FileFormatChecker.getParquetUrl(baseUrl,"parquet"),filePath,path,isAppend,appendPath,compactUuid);
//                return compactUuid; // 返回文件路径
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
        }

        return null;
    }

    // 读取文件前面内容，存储到数据库中，供页面展示使用
    private void processUploadedFileInitLine(String fileId, String filePath) throws Exception {
        List<Map<String, Object>> fileContext = DealFileContentUtils.parseFiles(new File(filePath), Constant.INIT_FILE_LINE);
        List<DatasetFileContentEntity> fileContentEntities = new ArrayList<>();

        for(int i = 0; i < fileContext.size(); i++){
            Map<String, Object> map = fileContext.get(i);
            DatasetFileContentEntity fileContentEntity = new DatasetFileContentEntity();
            fileContentEntity.setReferenceFileId(fileId);
            fileContentEntity.setFileContextId(map.getOrDefault("id","").toString());
            fileContentEntity.setFileContextRole(map.getOrDefault("role","").toString());
            fileContentEntity.setFileContextContent(map.getOrDefault("content","").toString());
            fileContentEntity.setFileContextPrompt(map.getOrDefault("prompt","").toString());
            fileContentEntity.setFileContextChosen(map.getOrDefault("chosen","").toString());
            fileContentEntity.setFileContextRejected(map.getOrDefault("rejected","").toString());
            fileContentEntity.setFileContextOutput(map.getOrDefault("output","").toString());
            fileContentEntity.setLineNumber(Integer.parseInt(map.getOrDefault("line_number","0").toString()));

            fileContentEntities.add(fileContentEntity);
        }
        fileContentService.insertBatch(fileContentEntities);
    }


    // 合并文件分片
    private FileVo mergeChunks(String fileName, int totalChunks, String uploadDir) throws IOException {
        Path finalFilePath = Paths.get(uploadDir, fileName);
        try (OutputStream outputStream = new FileOutputStream(finalFilePath.toFile(), true)) {
            for (int i = 0; i < totalChunks; i++) {
                Path chunkPath = Paths.get(uploadDir, fileName + ".part" + i);
                Files.copy(chunkPath, outputStream);
                Files.delete(chunkPath); // 删除已合并的分片
            }
        }

        // 合并完成后，准备上传文件到平台
        try (InputStream inputStream = Files.newInputStream(finalFilePath)) {
            // 假设 fileStorageService.upload() 是上传文件的方法
            // 这里传入的文件信息和输入流需要根据实际情况调整
            FileVo fileVo = fileStorageService.upload(fileName, "", "", inputStream);
            // 仅在上传成功后删除合并的文件
            Files.delete(finalFilePath);
            return fileVo; // 返回上传后的 FileVo 对象
        }
    }


}