
export const queryPageApi = (params) => {
  return {
    url: 'ai/dataset-version/page',
    method: 'get',
    params,
  }
}


// 查询文件详情 - 改为分页读取
export const queryFileDetailApi = (params) => {
  return {
    url: 'ai/dataset/data/page',
    method: 'get',
    params
  }
}

export const deleteBatchApi = (data) => {
  return {
    url: 'ai/dataset-version/deletion',
    method: 'post',
    data
  }
}

// 删除单个数据集版本
export const deleteApi = (params) => {
  return {
    url: 'ai/dataset-version/delete/' + params.id + '?deleteData=' + params.deleteData,
    method: 'post',
  }
}

export const addApi = (data) => {
  return {
    url: 'ai/dataset-version/create',
    method: 'post',
    data,
  }
}

// 获取数据集版本列表
export const queryListApi = (params) => {
  return {
    url: 'ai/dataset-version/list',
    method: 'get',
    params
  }
}

// 根据文件id分页获取对话详情
export const queryPageDetailApi = (params) => {
  return {
    url: 'ai/dataset-version/file-context-desc',
    method: 'get',
    params,
  }
}

// 更新数据集版本（用于发布/取消发布等操作）
export const editApi = (data) => {
  return {
    url: 'ai/dataset-version/update',
    method: 'post',
    data
  }
}



