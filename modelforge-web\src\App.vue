<template>
  <div id="app" :class="getClass()">
    <router-view></router-view>
    <db-dialog v-if="!hisDbdialog"></db-dialog>
    <lodop-vue
      ref="lodopRef"
      v-if="lodopRender"
      :lodopName="lodopName"
      :code="lodopCode"
      :param="lodopParam"
      :lodopElements="lodopElements"
      :lodopElementsData="elementsData"
      :allParams="allParams"
      style="visibility: hidden; position: fixed; z-index: -99999; left: 0; top: 0"
    ></lodop-vue>
  </div>
</template>

<script>
// import { clearAll } from '@base/utils/base/storage-util'
// import AuthConstant from '@base/constant/auth-constant.js'
import globalMixin from '@/mixins/globalMixin'
import lodopVue from '@base/views/print-design/template-design/index.vue'
import openMenu from '@base/utils/base/menu'
import UserConstant from '@base/constant/user-constant.js'
import { initializeCmdShell } from '@base/utils/websys.js' //医为客户端检测插件(同时支持Windows和Linux)
import dbDialog from '@base/components/DHCWebBrowser-dialog' //医为客户端弹窗
import { returnGlobalValue } from '@base/utils'

export default {
  name: 'App',
  mixins: [globalMixin],
  components: {
    dbDialog,
    lodopVue
  },
  data() {
    return {
      // 如果为true,则隐藏dbDialog,由his进行弹窗提醒
      hisDbdialog: returnGlobalValue('VUE_APP_HIS_DBDIALOG'),
      unbindMessage: null,
      lodopCode: '',
      lodopParam: '',
      lodopName: '',
      lodopRender: false,
      iframeFlag: true,
      lodopElements: [],
      elementsData: {},
      allParams: {}
    }
  },
  created() {
    // fix-hos 每次进入系统都更新登录类型缓存
    this.getLoginType()
    // 全局添加一个openMenu方法,传入传入menu对象或menu.name(code)
    window.openMenu = openMenu.bind(this)
    // 添加hos_前缀,防止被同名方法覆盖
    window.hos_openMenu = openMenu.bind(this)
    // 在其他页面通过postMessage,调用编辑菜单弹窗
    this.unbindMessage = this.$messageEvent.on(this.messageHandle)
    // 只保存biz下的vuex内容
    if (this.$ls.get('app')) {
      this.$store.state.app = this.$ls.get('app')
    }
    // 在页面刷新时将vuex-biz里的信息保存到sessionStorage里
    window.addEventListener('beforeunload', () => {
      this.$ls.set('app', this.$store.state.app)
    })
    // 在页面加载时读取sessionStorage里的状态信息
    // if (sessionStorage.getItem("store")) {
    //   this.$store.replaceState(
    //     Object.assign(
    //       {},
    //       this.$store.state,
    //       JSON.parse(sessionStorage.getItem("store"))
    //     )
    //   );
    // }
    //
    // // 在页面刷新时将vuex里的信息保存到sessionStorage里
    // window.addEventListener("beforeunload", () => {
    //   sessionStorage.setItem("store", JSON.stringify(this.$store.state));
    // });
  },
  mounted() {
    // 调用打开医为客户端插件，测试本地是否安装医为客户端
    const platform = window.navigator.platform
    const windowsPlatforms = ['Win32', 'Win64', 'Windows', 'WinCE']
    if (windowsPlatforms.includes(platform)) {
      // Windows系统
      this.getIpMac()
    } else if (/Linux/i.test(platform)) {
      // linux系统
      // 对linux不做isOpenDb判断,默认创建此ws连接
      this.initializeApplication()
    }
    this.addIframeEvent()
  },
  methods: {
    async getIpMac() {
      let isOpenDb = this.$ls.get('isOpenDb')
      // 未被接口赋值时,默认值为null.若不是boolean,表示还未请求接口获取数据.因此先请求接口.
      if (typeof isOpenDb !== 'boolean') {
        const { code, data } = await this.$api('base.index.dbDialogShowData')
        if (code == '200') {
          this.$ls.set('isOpenDb', data)
          isOpenDb = data
        }
      }
      // 此处判断全等于false,确保是接口返回的boolean
      if (isOpenDb === false) return
      // 判断sessionStorage里是否有Ip和Mac字段.如果没有则调用接口去获取
      const IP = this.$ls.get(UserConstant.IP)
      const MAC = this.$ls.get(UserConstant.Mac)
      if (!IP || !MAC) {
        try {
          const { status, rtn } = await this.$api('base.websys.cmd')
          if (status == '200') {
            const config = JSON.parse(rtn)
            this.$ls.set(UserConstant.IP, config.IP)
            this.$ls.set(UserConstant.HostName, config.HostName)
            this.$ls.set(UserConstant.Mac, config.Mac)
          }
        } catch (error) {
          // error表示获取cmd的接口报错,可能是未安装或未运行.在此处进行弹窗提醒
          this.$store.commit('SET_SHOW_DBDIALOG', true)
        }
      }
    },
    messageHandle(e) {
      const { type, data } = e.data
      if (type === 'editMenu') {
        this.openEditMenu(data)
      }
    },
    openEditMenu(id) {
      this.$store.commit('OPEN_DIALOG', {
        component: require('@base/views/sys/resource/edit.vue').default,
        _uid: 'resourceEditDialogGloble',
        props: {
          id: id,
          status: 'edit:',
          class: 'dialog-form-tabs'
        }
      })
    },
    async initializeApplication() {
      const CmdShell = await initializeCmdShell()
      // 可在此处调用其它方法，例如:

      CmdShell.default.CmdShell.notReturn = 0
      var ip = ''
      var hostName = ''
      var mac = ''
      var state = 404

      // 获得 IP,MAC,计算机名
      CmdShell.default.CmdShell.GetConfig(function (data) {
        state = 200
        if (typeof data === 'string') {
          var json = JSON.parse(data)
          ip = json.IP
          hostName = json.HostName
          mac = json.Mac
        } else {
          ip = data.IP
          hostName = data.HostName
          mac = data.Mac
        }
      })
      this.$ls.set(UserConstant.IP, ip)
      this.$ls.set(UserConstant.Mac, mac)
      this.$ls.set(UserConstant.HostName, hostName)
    },
    addIframeEvent() {
      const _this = this
      const lodopFunc = ['previewFn', 'printFn', 'getHtmlStr', 'getBase64']
      window.addEventListener(
        'message',
        (event) => {
          if (event.data.funcName && lodopFunc.includes(event.data.funcName)) {
            this.lodopRender = true
            this.lodopCode = event.data.params.code
            this.lodopParam = event.data.params.param
            this.lodopName = event.data.params.lodopName
            this.lodopElements = event.data.params.elements || []
            this.elementsData = event.data.params.elementsData || {}
            this.allParams = event.data.params
            this.$nextTick(() => {
              _this.$refs.lodopRef[event.data.funcName]()
            })
          } else if (event.data.funcName === 'filesPrint' || event.data.funcName === 'filesPreview') {
            this.lodopRender = true
            this.$nextTick(() => {
              _this.$refs.lodopRef.filesPrintPreview(event.data.params, event.data.funcName)
            })
          } else {
            return
          }
        },
        false
      )
    },
    getClass() {
      let result = []
      if (navigator.userAgent.indexOf('Chrome/49') !== -1) {
        result.push('chrome49')
      }
      return result
    },
    beforeunloadHandler(e) {
      e = e || window.event
      // if (!(window.performance && window.performance.navigation.type === 1)) { // 如果不是刷新是关闭浏览器窗口
      e.preventDefault()
      e.returnValue = '自定义文本'
      return '自定义文本'
      // } else {
      //   return true
      // }
    },
    // fix-hos
    getLoginType() {
      this.$api('biz.index.getLoginModel').then(res => {
        localStorage.setItem('loginType', JSON.stringify(res.data))
      })
    }
  },
  beforeDestroy() {
    this.unbindMessage()
    window.removeEventListener('beforeunload', (e) => this.beforeunloadHandler(e))
  }
}
</script>

<style>
@import './sys/hos-app-base/assets/style/custom.css';
@import './sys/hos-app-base/assets/style/font-awesome-4.7.0/css/font-awesome.min.css';
html,
body {
  margin: 0 !important;
  padding: 0;
}
.github-corner:hover .octo-arm {
  animation: octocat-wave 560ms ease-in-out;
}
@keyframes octocat-wave {
  0%,
  100% {
    transform: rotate(0);
  }
  20%,
  60% {
    transform: rotate(-25deg);
  }
  40%,
  80% {
    transform: rotate(10deg);
  }
}
@media (max-width: 500px) {
  .github-corner:hover .octo-arm {
    animation: none;
  }
  .github-corner .octo-arm {
    animation: octocat-wave 560ms ease-in-out;
  }
}
</style>
