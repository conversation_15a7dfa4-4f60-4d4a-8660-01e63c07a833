package com.mediway.platform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mediway.platform.commons.config.CsmCacheHelper;
import com.mediway.platform.commons.config.CsmUserCacheHelper;
import com.mediway.platform.controller.PreprocessingController;
import com.mediway.platform.hos.vo.HosUserVO;
import com.mediway.platform.mapper.DataProcessingTaskMapper;
import com.mediway.platform.mapper.DatasetMapper;
import com.mediway.platform.mapper.DatasetVersionMapper;
import com.mediway.platform.model.dto.DataProcessingTaskDTO;
import com.mediway.platform.model.entity.DataProcessingTaskEntity;
import com.mediway.platform.model.entity.DatasetEntity;
import com.mediway.platform.model.entity.DatasetVersionEntity;
import com.mediway.platform.model.vo.DataProcessingTaskVO;
import com.mediway.platform.process.DataAugmentationConfig;
import com.mediway.platform.process.PreprocessTaskConfig;
import com.mediway.platform.process.PreprocessTaskManager;
import com.mediway.platform.process.PreprocessType;
import com.mediway.platform.service.DataProcessingTaskService;
import com.mediway.platform.service.DatasetService;
import com.mediway.platform.service.DatasetVersionService;
import com.mediway.platform.utils.ConvertUtils;
import com.mediway.platform.utils.VersionUtils;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 数据处理任务服务层实现类，实现具体业务逻辑
 */
@Service
public class DataProcessingTaskServiceImpl extends ServiceImpl<DataProcessingTaskMapper, DataProcessingTaskEntity>
        implements DataProcessingTaskService {

    private final Map<String, PreprocessTaskConfig> taskConfigs = new ConcurrentHashMap<>();

    @Resource
    private PreprocessTaskManager taskManager;

    @Resource
    private DatasetVersionMapper datasetVersionMapper;

    @Resource
    private DatasetMapper datasetMapper;

    /**
     * 创建数据处理任务（将 DTO 转换为实体并保存）
     * @param dto 数据处理任务 DTO
     */
    @Override
    public void createTask(DataProcessingTaskDTO dto) {
        DataProcessingTaskEntity task = ConvertUtils.convertToEntity(dto, DataProcessingTaskEntity.class);
        /*
         * 获取 数据集 名称
         */
        DatasetEntity datasetEntity = datasetMapper.selectById(dto.getDatasetId());
        task.setDatasetName(datasetEntity.getName());

        // 获取当前版本号
        DatasetVersionEntity versionEntity = datasetVersionMapper.selectById(dto.getDatasetVersionsIdBefore());
        String versionNumber = versionEntity.getVersionNumber();
        task.setDatasetVersionsNumBefore(versionNumber);
        /*
         * 新增版本
         */
        // 获取数据库中最大的版本号
        LambdaQueryWrapper<DatasetVersionEntity> maxVersionQueryWrapper = Wrappers.lambdaQuery();
        maxVersionQueryWrapper.eq(DatasetVersionEntity::getDatasetId, dto.getDatasetId())
                .orderByDesc(DatasetVersionEntity::getVersionNumber);
        List<DatasetVersionEntity> list = datasetVersionMapper.selectList(maxVersionQueryWrapper);
        String maxversionNumber = list.isEmpty() ? null : list.get(0).getVersionNumber();
        DatasetVersionEntity dvEntity = new DatasetVersionEntity();
        dvEntity.setDatasetId(task.getDatasetId());
        dvEntity.setVersionNumber(VersionUtils.newVersion(maxversionNumber));

        dvEntity.setFileId(versionEntity.getFileId());// 数据处理的时候，处理完成的是另一个版本，新版本中用的数据集版本文件 和 上一个一样。
        String accountId = CsmUserCacheHelper.me().getLoginPersonUser().getAccountId();

        dvEntity.setOperator(accountId);
        datasetVersionMapper.insert(dvEntity);

        task.setDatasetVersionsIdAfter(dvEntity.getId());
        task.setDatasetVersionsNumAfter(dvEntity.getVersionNumber());

        /*
         * 创建人
         */
//        String loginUserRoleName = CsmCacheHelper.me().getCurLoginUserRoleName();
        task.setOperator(accountId);


        this.save(task);

    }


    /**
     * 更新数据处理任务（根据 ID 更新实体）
     * @param entity 数据处理任务实体
     * @return 更新是否成功
     */
    @Override
    public boolean updateTask(DataProcessingTaskEntity entity) {
        return this.saveOrUpdate(entity); // 调用 MyBatis-Plus 根据 ID 更新
    }

    @Override
    public boolean startTask(DataProcessingTaskEntity entity){
        this.updateTask(entity);
        // 创建数据任务并启动
        TaskCreationRequest request = new TaskCreationRequest();

        Set<PreprocessType> types = EnumSet.noneOf(PreprocessType.class);
        for (String type : request.getPreprocessTypes()) {
            types.add(PreprocessType.valueOf(type.toUpperCase()));
        }

        DataAugmentationConfig augmentationConfig = new DataAugmentationConfig(
                request.getDependentSampleCount(),
                request.getSimilarityThreshold(),
                request.getGeneratedSampleCount(),
                request.getPromptTemplate()
        );

        String taskId = "task-" + entity.getId();
        PreprocessTaskConfig config = new PreprocessTaskConfig(
                taskId,
                request.getInputPath(),
                request.getOutputPath(),
                types,
                augmentationConfig,
                request.isExecuteImmediately()
        );

        taskConfigs.put(taskId, config);
        String finalTaskId = taskManager.createAndExecuteTask(config);
//        Map<String, Object> response = new HashMap<>();
//        response.put("taskId", finalTaskId);
//        response.put("status", "CREATED");
//        return response;

        return true;
    }

    /**
     * 删除数据处理任务（根据 ID 删除）
     * @param id 任务 ID
     * @return 删除是否成功
     */
    @Override
    public boolean deleteTask(String id) {
        return this.removeById(id); // 调用 MyBatis-Plus 根据 ID 删除
    }

    /**
     * 根据 ID 获取任务详情（转换为视图对象）
     * @param id 任务 ID
     * @return 任务视图对象，若不存在则返回 null
     */
    @Override
    public DataProcessingTaskVO getTaskById(String id) {
        DataProcessingTaskEntity entity = this.getById(id);
        if (entity == null) {
            return null;
        }
        String operator = entity.getOperator();
        HosUserVO byAccountId = CsmUserCacheHelper.me().getByAccountId(operator);
        if (byAccountId != null) {
            entity.setOperator(byAccountId.getAccountName());
        } else {
            entity.setOperator(operator); // 如果缓存中没有找到，使用原始的 operator
        }
        return ConvertUtils.convertToVO(entity, DataProcessingTaskVO.class); // 实体转视图对象
    }

    /**
     * 获取所有数据处理任务（转换为视图对象列表）
     * @return 任务视图对象列表
     */
    @Override
    public List<DataProcessingTaskVO> getAllTasks() {
        List<DataProcessingTaskEntity> entities = this.list(); // 获取所有实体
        return entities.stream()
                .map(entity -> {
                    DataProcessingTaskVO taskVO = ConvertUtils.convertToVO(entity, DataProcessingTaskVO.class);
                    String operator = entity.getOperator();
                    HosUserVO byAccountId = CsmUserCacheHelper.me().getByAccountId(operator);
                    if (byAccountId != null) {
                        taskVO.setOperator(byAccountId.getAccountName());
                    } else {
                        taskVO.setOperator(operator); // 如果缓存中没有找到，使用原始的 operator
                    }
                    return taskVO;
                })
                .collect(Collectors.toList()); // 批量转换为视图对象
    }

    /**
     * 分页查询数据处理任务（带条件过滤）
     * @param pageNum 当前页码
     * @param pageSize 每页记录数
     * @param taskName 任务名称（可选）
     * @param datasetId 数据集 ID（可选）
     * @param taskStatus 任务状态（可选）
     * @return 分页后的任务视图对象
     */
    @Override
    public IPage<DataProcessingTaskVO> getTaskPage(
            Integer pageNum,
            Integer pageSize,
            String taskName,
            String datasetId,
            String taskStatus) {
        // 创建分页对象
        IPage<DataProcessingTaskEntity> entityPage = new Page<>(pageNum, pageSize);
        // 创建查询条件
        LambdaQueryWrapper<DataProcessingTaskEntity> queryWrapper = new LambdaQueryWrapper<>();

        // 动态添加查询条件
        if (taskName != null && !taskName.isEmpty()) {
            queryWrapper.like(DataProcessingTaskEntity::getTaskName, taskName); // 模糊查询任务名称
        }
        if (datasetId != null && !datasetId.isEmpty()) {
            queryWrapper.eq(DataProcessingTaskEntity::getDatasetId, datasetId); // 精确查询数据集 ID
        }
        if (taskStatus != null && !taskStatus.isEmpty()) {
            queryWrapper.eq(DataProcessingTaskEntity::getTaskStatus, taskStatus); // 精确查询任务状态
        }

        // 执行分页查询
        entityPage = this.page(entityPage, queryWrapper);

        // 转换为视图对象分页结果
        IPage<DataProcessingTaskVO> voPage = new Page<>(pageNum, pageSize);
        voPage.setTotal(entityPage.getTotal()); // 总记录数
        voPage.setRecords(entityPage.getRecords().stream()
                .map(entity -> {
                    DataProcessingTaskVO taskVO = ConvertUtils.convertToVO(entity, DataProcessingTaskVO.class);
                    String operator = entity.getOperator();
                    HosUserVO byAccountId = CsmUserCacheHelper.me().getByAccountId(operator);
                    if (byAccountId != null) {
                        taskVO.setOperator(byAccountId.getAccountName());
                    } else {
                        taskVO.setOperator(operator); // 如果缓存中没有找到，使用原始的 operator
                    }
                    return taskVO;
                })
                .collect(Collectors.toList())); // 记录列表转换

        return voPage;
    }

    /**
     * 批量删除数据处理任务（根据 ID 列表）
     * @param ids 任务 ID 列表
     * @return 批量删除是否成功
     */
    @Override
    public boolean batchDeleteTasks(List<String> ids) {
        return this.removeByIds(ids); // 调用 MyBatis-Plus 批量删除
    }


    // 请求和响应类
    @Data
    static class TaskCreationRequest {
        private String inputPath;
        private String outputPath;
        private List<String> preprocessTypes;
        private int dependentSampleCount;
        private double similarityThreshold;
        private int generatedSampleCount;
        private String promptTemplate;
        private boolean executeImmediately;
    }
}