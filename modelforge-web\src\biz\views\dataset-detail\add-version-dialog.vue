<template>
  <div>
    <hos-form ref="form" :model="form" :rules="rules">
      <hos-form-item :label="$t('版本号')" prop="versionNumber" label-width="120px">
        <hos-input v-model="form.versionNumber" disabled />
      </hos-form-item>

      <hos-form-item :label="$t('版本说明')" prop="versionDescription" label-width="120px">
        <hos-input v-model="form.versionDescription" clearable />
      </hos-form-item>

      <hos-form-item :label="$t('数据来源')" prop="dataSource" label-width="120px">
        <hos-radio-group v-model="form.dataSource" style="vertical-align: center">
          <hos-radio :label="1">{{ $t('从版本继承') }}</hos-radio>
          <hos-radio :label="2">{{ $t('不从版本继承') }}</hos-radio>
        </hos-radio-group>
      </hos-form-item>

      <hos-form-item v-if="form.dataSource == 1" :label="$t('选择版本')" prop="extendVersionNum" label-width="120px">
        <hos-select v-model="form.extendVersionNum" clearable>
          <hos-option v-for="item in versionList" :key="item.id" :label="item.versionNumber" :value="item.id" />
        </hos-select>
      </hos-form-item>

      <!-- <hos-form-item v-if="form.dataSource == 2" :label="$t('数据文件')" prop="fileId" label-width="110px">
        <span v-show="form.fileId"
          >{{ form.fileId }}
          <tips style="margin-left: 5px" placement="top" :content="$t('文件已成功上传，此为文件id信息。')"
        /></span>
        <file-down-upload
          ref="dataFileUpload"
          :download-template-option="downloadTemplateOption"
          :upload-file-option="uploadFileOption"
          :beforeDownload="beforeDownload"
          :afterUpload="afterUpload"
        ></file-down-upload>
      </hos-form-item> -->
    </hos-form>

    <div slot="footer" class="dialog-footer">
      <hos-button @click="close">{{ $t('取消') }}</hos-button>
      <hos-button type="success" @click="submit">{{ $t('保存') }}</hos-button>
    </div>
  </div>
</template>

<script>
import { _debounce } from '@/utils/throttle.js'
import FileDownUpload from '../datasets-manage/file-down-upload.vue'

export default {
  name: 'addVersionDialog',
  props: ['id', 'versionList', 'lastVersionNum', 'datasetId', 'datasetType'],
  components: { FileDownUpload },
  data() {
    return {
      form: {
        datasetId: '',
        versionNumber: '',
        versionDescription: '',
        dataSource: 1, // 默认从版本继承
        extendVersionNum: '',
        fileId: ''
      },
      downloadTemplateOption: {
        isHide: false, // 是否隐藏下载
        method: 'GET', // 请求方式
        apiUrl: 'ai/dataset/downloadFile', // 下载模板Api对应的接口地址
        params: {
          type: ''
        }, // 拼到请求路径上的参数
        data: {}, // body参数 POST请求时用到
        templateFileName: this.$t('数据集导入模板')
      },
      uploadFileOption: {
        uploadTypeState: false, // 是否需要选择导入数据类型,目前只有新增场景,后续看接口适配情况
        method: 'POST',
        apiUrl: 'ai/dataset/upload',
        params: {},
        data: {
          accountId: '',
          subjectId: ''
        }
      }
    }
  },
  async created() {
    // if (this.id) {
    //   const { data } = await this.$api('biz.datasetsManage.queryDetailApi', this.id)
    //   this.form = { ...data }
    // }
    this.form.versionNumber = 'V' + (this.lastVersionNum + 1)
    this.form.datasetId = this.datasetId
  },
  computed: {
    rules() {
      if (this.form.dataSource == 1) {
        return {
          extendVersionNum: [{ required: true, message: this.$t('继承版本必选'), trigger: 'change' }]
        }
      } else {
        // return {
        //   fileId: [{ required: true, message: this.$t('数据文件必须上传'), trigger: 'change' }]
        // }
        return {}
      }
    }
  },
  methods: {
    close() {
      this.$store.commit('CLOSE_DIALOG', { _uid: 'addVersionDialog' })
    },
    submit: _debounce(function () {
      this.$refs.form.validate(async (valid) => {
        // if (this.form.dataSource == 2 && !this.form.fileId) {
        //   return this.$message.warning(this.$t('请上传数据文件'))
        // }
        if (!valid) return

        const apiName = this.id ? 'editApi' : 'addApi'
        const { code, msg } = await this.$api(`biz.datasetVersion.${apiName}`, this.form)

        if (code == 200) {
          this.$message.success(msg)
          this.$store.commit('UPDATE_TABLE', { _uid: 'leftDatasetVersionTable' })
        }
        this.close()
      })
    }),
    beforeDownload() {
      // 提示先选择数据集类型
      this.downloadTemplateOption.params.type = this.datasetType
      this.$refs.dataFileUpload.downloadTemplate()
    },
    afterUpload(data) {
      console.log(data, '130')
      this.form.fileId = data
    }
  }
}
</script>

<style lang="scss" scoped>
.hos-radio-group {
  vertical-align: middle;
}
</style>