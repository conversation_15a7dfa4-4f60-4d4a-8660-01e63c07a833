<template>
  <div class="training-method-tips-dialog">
    <div class="dialog-content">
      <div v-if="loading" class="loading-container">
        <span>加载中...</span>
      </div>
      <div v-else-if="markdownContent" class="markdown-content" v-html="renderedMarkdown"></div>
      <div v-else class="error-container">
        <span>暂无说明内容</span>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer tc">
      <hos-button @click="close">{{ $t('关闭') }}</hos-button>
    </div>
  </div>
</template>

<script>
import marked from 'marked'
import DOMPurify from 'dompurify'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'

export default {
  name: 'TrainingMethodTipsDialog',
  data() {
    return {
      loading: true,
      markdownContent: ''
    }
  },
  computed: {
    renderedMarkdown() {
      if (!this.markdownContent) return ''
      
      // 配置marked选项
      marked.setOptions({
        highlight: function(code, lang) {
          if (lang && hljs.getLanguage(lang)) {
            try {
              return hljs.highlight(code, { language: lang }).value
            } catch (err) {}
          }
          return hljs.highlightAuto(code).value
        },
        breaks: true,
        gfm: true
      })
      
      // 解析markdown并清理HTML
      const rawHtml = marked.parse(this.markdownContent)
      return DOMPurify.sanitize(rawHtml)
    }
  },
  created() {
    this.fetchDataUseageTips()
  },
  methods: {
    async fetchDataUseageTips() {
      try {
        this.loading = true
        
        // 模拟数据，直接返回markdown内容
        this.markdownContent = `# 数据用途说明

## SFT文本生成（Supervised Fine-tuning）

**定义：** SFT文本生成是通过监督学习的方式，使用输入-输出对数据来训练模型生成特定格式或风格文本的方法。

**数据格式：**
- 通常为问答对、指令-响应对或输入-输出对
- 每个样本包含明确的输入和期望的输出
- 数据标注相对简单，只需提供正确答案

**特点：**
- **训练直接：** 直接学习输入到输出的映射关系
- **数据要求低：** 只需要输入-输出对，标注成本较低
- **收敛快速：** 有明确的学习目标，训练过程相对稳定
- **适用性广：** 适合各种文本生成任务

**优势：**
- 实现简单，易于理解和实施
- 对数据质量要求相对较低
- 训练效率高，资源消耗适中
- 能够快速适应特定任务或领域

**适用场景：**
- 问答系统训练
- 指令跟随能力提升
- 特定领域知识学习
- 文本风格转换
- 摘要生成、翻译等任务

## DPO文本生成（Direct Preference Optimization）

**定义：** DPO文本生成是通过人类偏好数据直接优化模型生成质量的方法，无需显式的奖励模型。

**数据格式：**
- 偏好对数据：每个样本包含同一输入的两个输出
- 标注人员需要标记哪个输出更好（preferred vs rejected）
- 数据标注更加复杂，需要质量判断

**技术原理：**
- 直接使用偏好数据优化模型参数
- 避免了传统RLHF中训练奖励模型的步骤
- 通过对比学习提升生成质量

**特点：**
- **质量导向：** 专注于提升输出的整体质量和用户满意度
- **偏好学习：** 学习人类的偏好模式，而非简单的正确答案
- **无需奖励模型：** 简化了传统RLHF的复杂流程
- **效果显著：** 在提升模型对话质量方面效果突出

**优势：**
- 显著提升生成文本的质量和可用性
- 更好地对齐人类偏好和价值观
- 训练过程比传统RLHF更稳定
- 能够学习复杂的质量标准

**适用场景：**
- 对话系统质量优化
- 创意写作能力提升
- 减少有害或不当输出
- 提升回答的有用性和准确性
- 个性化内容生成

## 主要区别对比

| 特征 | SFT文本生成 | DPO文本生成 |
|------|-------------|-------------|
| **数据类型** | 输入-输出对 | 偏好对比数据 |
| **学习目标** | 学习正确答案 | 学习质量偏好 |
| **标注复杂度** | 低（提供答案） | 高（质量判断） |
| **训练难度** | 简单 | 中等 |
| **效果重点** | 任务完成度 | 输出质量 |
| **资源消耗** | 中等 | 较高 |

## 选择建议

### 推荐使用SFT，如果：
- 您需要快速让模型学习特定任务
- 有明确的输入-输出标准
- 标注资源有限
- 注重训练效率
- 模型需要学习新的知识或技能

### 推荐使用DPO，如果：
- 已有基础能力的模型需要质量提升
- 关注用户体验和满意度
- 有足够的人力进行质量标注
- 需要减少不当或低质量输出
- 追求更自然、更符合人类偏好的生成效果

### 组合使用策略：
1. **先SFT后DPO：** 先用SFT学习基础能力，再用DPO提升质量
2. **分阶段训练：** 针对不同需求采用不同方法
3. **任务细分：** 知识学习用SFT，质量优化用DPO

## 数据示例

### SFT数据示例

\`\`\`json
{
  "input": "请解释什么是机器学习？",
  "output": "机器学习是一种人工智能技术，通过算法让计算机从数据中学习模式，无需明确编程即可做出预测或决策。主要包括监督学习、无监督学习和强化学习三种类型。"
}
\`\`\`

### DPO数据示例

\`\`\`json
{
  "input": "请解释什么是机器学习？",
  "preferred": "机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下从经验中学习。通过分析大量数据，机器学习算法可以识别模式、做出预测并改进性能。常见应用包括推荐系统、图像识别和自然语言处理。",
  "rejected": "机器学习就是让机器自己学习的技术，很复杂，有很多算法。"
}
\`\`\`

## 实施建议

1. **数据质量控制：** 无论选择哪种方法，都要确保数据质量
2. **渐进式训练：** 可以先用SFT建立基础，再用DPO精调
3. **评估指标：** SFT关注任务准确性，DPO关注用户满意度
4. **资源规划：** DPO需要更多的标注和计算资源

**注意事项：**
- SFT适合快速原型和基础能力建设
- DPO适合已有模型的质量提升
- 两种方法可以结合使用，发挥各自优势
- 选择时要考虑具体应用场景和资源约束`

        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 500))
        
      } catch (error) {
        console.error('获取训练方式说明失败:', error)
        this.$message.error('获取说明内容失败')
      } finally {
        this.loading = false
      }
    },
    
    close() {
      this.$store.commit('CLOSE_DIALOG', { _uid: 'dataUseageTipsDialog' })
    }
  }
}
</script>

<style lang="scss" scoped>
.training-method-tips-dialog {
  height: 100%;
  padding: 0;
  
  .dialog-content {
    height: calc(100% - 80px);
    overflow-y: auto;
    padding: 20px;
  }
  
  .loading-container,
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #666;
  }
  
  .markdown-content {
    /* ========== Markdown优化样式 - 开始 ========== */
    line-height: 1.6;
    color: #333;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
    
    ::v-deep {
      /* 一级标题样式 */
      h1 {
        font-size: 28px;
        font-weight: 700;
        margin: 30px 0 20px 0;
        padding-bottom: 12px;
        border-bottom: 3px solid #007bff;
        color: #1a1a1a;
      }
      
      /* 二级标题样式 - 带左侧彩色条 */
      h2 {
        font-size: 22px;
        font-weight: 600;
        margin: 25px 0 15px 0;
        color: #2c3e50;
        position: relative;
        padding-left: 15px;
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 20px;
          background: linear-gradient(135deg, #007bff, #0056b3);
          border-radius: 2px;
        }
      }
      
      /* 三级标题样式 - 左边框 */
      h3 {
        font-size: 18px;
        font-weight: 600;
        margin: 20px 0 12px 0;
        color: #34495e;
        padding-left: 8px;
        border-left: 3px solid #17a2b8;
      }
      
      /* 段落样式 */
      p {
        margin: 12px 0;
        line-height: 1.7;
        color: #444;
      }
      
      /* 列表样式 */
      ul {
        margin: 15px 0;
        padding-left: 20px;
        
        li {
          margin: 6px 0;
          color: #444;
        }
      }
      
      /* 粗体样式 */
      strong {
        font-weight: 700;
        color: #2c3e50;
      }
      
      /* 斜体样式 */
      em {
        font-style: italic;
        color: #6c757d;
      }
      
      /* 行内代码样式 */
      code {
        background: #f1f3f4;
        color: #e83e8c;
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 13px;
        font-weight: 500;
      }
      
      /* 代码块样式 */
      pre {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 16px;
        margin: 16px 0;
        overflow-x: auto;
        
        code {
          background: none;
          color: inherit;
          padding: 0;
          font-size: 14px;
          line-height: 1.5;
        }
      }
      
      /* 链接样式 */
      a {
        color: #007bff;
        text-decoration: none;
        
        &:hover {
          color: #0056b3;
          text-decoration: underline;
        }
      }
      
      /* 引用块样式 */
      blockquote {
        border-left: 4px solid #ddd;
        padding-left: 16px;
        margin: 16px 0;
        color: #666;
        font-style: italic;
      }
    }
    /* ========== Markdown优化样式 - 结束 ========== */
  }
  
  .dialog-footer {
    display: flex;
    justify-content: center;
    padding: 15px 20px;
    border-top: 1px solid #eee;
    background-color: #fff;
  }
}
</style>