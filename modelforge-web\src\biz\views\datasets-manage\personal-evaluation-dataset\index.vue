<!-- 个人评测集 -->
<template>
  <div class="h-fit">
    <biz-crud ref="listPage" :option="crudOption" tbCode="datasetConfig" uid="datasetManageTable">
      <template #buttonLeftOnTable>
        <hos-button-group class="hos">
          <hos-button @click="addDialog()" icon="hos-icom-add" v-has-permi="{ key: '' }">{{ $t('新增') }}</hos-button>
          <!-- <hos-button @click="$refs.listPage.deletion()" icon="hos-icom-cancel" v-has-permi="{ key: '' }">{{
            $t('删除')
          }}</hos-button> -->
        </hos-button-group>
      </template>

      <template #operation="{ row }">
        <!-- <hos-tooltip class="pl5 pr5" :content="$t('编辑')" v-has-permi="{ key: '' }"
          ><i class="hos-icom-edit" @click="editDialog(row)"></i
        ></hos-tooltip> -->
        <hos-tooltip class="pl5 pr5" :content="$t('查看')" v-has-permi="{ key: '' }">
          <i class="hos-icon-view" @click="view(row)"></i>
        </hos-tooltip>
        <!-- 启用/禁用按钮 -->
        <hos-tooltip v-if="row.isActivity" class="pl5 pr5" :content="$t('禁用')" v-has-permi="{ key: '' }">
          <i class="hos-icom-forbid" @click="toggleActivity(row, 0)"></i>
        </hos-tooltip>
        <hos-tooltip v-else class="pl5 pr5" :content="$t('启用')" v-has-permi="{ key: '' }">
          <i class="hos-icom-triangle-green-right" @click="toggleActivity(row, 1)"></i>
        </hos-tooltip>
        <hos-tooltip class="pl5 pr5" :content="$t('删除')" v-has-permi="{ key: '' }"
          ><i class="hos-icom-cancel" @click="deleteRow(row)"></i
        ></hos-tooltip>
      </template>
    </biz-crud>
    <hos-biz-dialog :title="editDialogTitle" width="50%" uid="DatasetDialog" :close-on-click-modal="false" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      typeList: [],
      dataUsageList: [],
      editDialogTitle: ''
    }
  },
  created() {
    this.getDictItemByCode('datasets-type', 'typeList')
    this.getDictItemByCode('data-usage', 'dataUsageList')
  },
  computed: {
    crudOption() {
      return {
        queryFormFields: [
          { inputType: 'input', label: this.$t('数据集名称'), field: 'name' },
        //   {
        //     inputType: 'select',
        //     label: this.$t('类型'),
        //     field: 'type',
        //     SelectOption: { localOptions: this.typeList, label: 'label', option: 'value' }
        //   },
          {
            inputType: 'select',
            label: this.$t('数据用途'),
            field: 'dataUsage',
            SelectOption: { localOptions: this.dataUsageList, label: 'label', option: 'value' }
          }
        ],
        buttons: {
          query: { api: (p) => this.$api('biz.datasetsManage.queryListApi', { ...p, type: 2 }) },
          delete: { api: (p) => this.$api('biz.datasetsManage.deleteBatchApi', p), isShow: false }
        },
        columns: [
          { type: 'selection', width: '50px', align: 'center' },
          { label: this.$t('数据集名称'), field: 'name' },
          {
            label: this.$t('类型'),
            prop: ({ row }) => {
              return this.listToMap(this.typeList)['2']
            }
          },
          {
            label: this.$t('数据用途'),
            prop: ({ row }) => {
              return this.listToMap(this.dataUsageList)[row['dataUsage']]
            }
          },
          { label: this.$t('存储目录'), field: 'storageDirectory' },
          { label: this.$t('创建时间'), field: 'createTime', width: '160px' },
          { label: this.$t('操作'), width: '150px', slotName: 'operation' }
        ]
      }
    }
  },
  methods: {
    addDialog() {
      this.editDialogTitle = this.$t('新增')
      this.$store.commit('OPEN_DIALOG', {
        component: require('../datasets-dialog.vue').default,
        _uid: 'DatasetDialog',
        props: {
          id: '',
          typeList: this.typeList,
          dataUsageList: this.dataUsageList,
          type: 2
        }
      })
    },
    editDialog(row) {
      this.editDialogTitle = this.$t('编辑')
      this.$store.commit('OPEN_DIALOG', {
        component: require('../datasets-dialog.vue').default,
        _uid: 'DatasetDialog',
        props: {
          id: row.id,
          typeList: this.typeList
        }
      })
    },
    deleteRow(row) {
      this.$confirm(`是否删除数据集【${row.name}】？将同时删除数据集下的所有版本数据。`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'delete'
      })
        .then(() => {
          this.$api('biz.datasetsManage.deleteBatchApi', [row.id]).then((res) => {
            if (res.code == 200) {
              this.$message.success(this.$t('操作成功'))
              this.$store.commit('UPDATE_TABLE', { _uid: 'datasetManageTable' })
            }
          })
        })
        .catch(() => {})
    },
    view(row) {
      this.$router.push({
        name: 'dataset-detail',
        query: {
          title: row.name,
          datasetId: row.id
        }
      })
    },
    // 启用/禁用数据集
    toggleActivity(row, isActivity) {
      const action = isActivity ? '启用' : '禁用'
      this.$confirm(`确定要${action}数据集【${row.name}】吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 准备更新数据，将原有数据连同isActivity一起传递
          const updateData = {
            ...row,
            isActivity: isActivity
          }
          
          this.$api('biz.datasetsManage.editApi', updateData).then((res) => {
            if (res.code == 200) {
              this.$message.success(`${action}成功`)
              this.$store.commit('UPDATE_TABLE', { _uid: 'datasetManageTable' })
            } else {
              this.$message.error(res.msg || `${action}失败`)
            }
          }).catch(error => {
            console.error(`${action}失败:`, error)
            this.$message.error(`${action}失败，请稍后重试`)
          })
        })
        .catch(() => {})
    }
  }
}
</script>