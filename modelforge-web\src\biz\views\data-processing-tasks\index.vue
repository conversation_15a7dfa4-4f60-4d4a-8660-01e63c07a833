<!-- 文件：task-list.vue -->
<template>
  <div class="h-fit">
    <biz-crud ref="listPage" :option="crudOption" tbCode="taskConfig" uid="taskManageTable">
      <template #buttonLeftOnTable>
        <hos-button-group class="hos">
          <hos-button @click="addDialog()" icon="hos-icom-add" v-has-permi="{ key: '' }">{{ $t('新增处理任务') }}</hos-button>
          <hos-button @click="$refs.listPage.deletion()" icon="hos-icom-cancel" v-has-permi="{ key: '' }">{{
            $t('删除')
          }}</hos-button>
        </hos-button-group>
      </template>

      <template #datasetVersionsNameBefore="{ row }">
        {{ row.datasetName + '（' + row.datasetVersionsNumBefore + '）' }}
      </template>

      <template #datasetVersionsNameAfter="{ row }">
        {{ row.datasetName + '（' + row.datasetVersionsNumAfter + '）' }}
      </template>

      <template #operation="{ row }">
        <hos-tooltip class="pl5 pr5" :content="$t('查看')"
          ><i class="hos-icom-eye" @click="showDetail(row)"></i
        ></hos-tooltip>

        <hos-tooltip class="pl5 pr5" :content="$t('编辑')" v-has-permi="{ key: '' }"
          ><i class="hos-icom-edit" @click="editDialog(row)"></i
        ></hos-tooltip>
        <hos-tooltip class="pl5 pr5" :content="$t('执行')" v-has-permi="{ key: '' }"
          ><i class="hos-icom-run" @click="runTask(row)"></i
        ></hos-tooltip>
        <hos-tooltip class="pl5 pr5" :content="$t('删除')" v-has-permi="{ key: '' }"
          ><i class="hos-icom-cancel" @click="deleteRow(row)"></i
        ></hos-tooltip>
      </template>
    </biz-crud>
    <hos-biz-dialog :title="editDialogTitle" width="60%" uid="TaskDialog" :close-on-click-modal="false" />
    <hos-biz-dialog title="任务详情" width="80%" uid="TaskDetailDialog" :close-on-click-modal="false" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      taskStatusList: [],
      editDialogTitle: '',
      datasetList: []
    }
  },
  computed: {
    crudOption() {
      return {
        queryFormFields: [
          { inputType: 'input', label: this.$t('任务名称'), field: 'taskName' },
          {
            inputType: 'select',
            label: this.$t('任务状态'),
            field: 'taskStatus',
            SelectOption: { localOptions: this.taskStatusList, label: 'label', option: 'value' }
          }
        ],
        buttons: {
          query: { api: (p) => this.$api('biz.dataProcessingManage.queryListApi', p) },
          delete: { api: (p) => this.$api('biz.dataProcessingManage.deleteBatchApi', p), isShow: false }
        },
        columns: [
          { type: 'selection', width: '50px', align: 'center' },
          { label: this.$t('任务名称'), field: 'taskName' },
          {
            label: this.$t('处理前数据集'),
            slotName: 'datasetVersionsNameBefore'
          },
          {
            label: this.$t('处理后数据集'),
            slotName: 'datasetVersionsNameAfter'
          },
          {
            label: this.$t('任务状态'),
            prop: ({ row }) => {
              return this.listToMap(this.taskStatusList)[row['taskStatus']]
            }
          },
          { label: this.$t('创建时间'), field: 'createTime' },
          { label: this.$t('创建者'), field: 'operator' },
          { label: this.$t('操作'), slotName: 'operation' }
        ]
      }
    }
  },
  created() {
    this.getCascadeDataset()
    this.getDictItemByCode('task-status', 'taskStatusList')
  },
  methods: {
    getCascadeDataset() {
      this.$api('biz.datasetsManage.queryAllCascadeApi', {
        type: 1 // 只查询训练集
      }).then((res) => {
        const { code, data } = res
        if (code == 200) {
          this.datasetList = data
        }
      })
    },
    addDialog() {
      this.editDialogTitle = this.$t('新增')
      this.$store.commit('OPEN_DIALOG', {
        component: require('./task-dialog.vue').default,
        _uid: 'TaskDialog',
        props: {
          id: '',
          taskStatusList: this.taskStatusList,
          datasetList: this.datasetList
        }
      })
    },
    editDialog(row) {
      this.editDialogTitle = this.$t('修改')
      this.$store.commit('OPEN_DIALOG', {
        component: require('./task-dialog.vue').default,
        _uid: 'TaskDialog',
        props: {
          id: row.id,
          taskStatusList: this.taskStatusList,
          datasetList: this.datasetList
        }
      })
    },
    runTask(row) {
      this.$confirm(`是否执行处理任务【${row.taskName}】？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'delete'
      })
        .then(() => {
          this.$api('biz.dataProcessingManage.editApi', {
            ...row,
            taskStatus: 2
          }).then((res) => {
            if (res.code == 200) {
              this.$message.success(this.$t('操作成功'))
              this.$store.commit('UPDATE_TABLE', { _uid: 'taskManageTable' })
            }
          })
        })
        .catch(() => {})
    },
    deleteRow(row) {
      this.$confirm(`是否删除处理任务【${row.taskName}】？删除任务不会影响已生成后的数据集及版本数据。`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'delete'
      })
        .then(() => {
          this.$api('biz.dataProcessingManage.deleteBatchApi', [row.id]).then((res) => {
            if (res.code == 200) {
              this.$message.success(this.$t('操作成功'))
              this.$store.commit('UPDATE_TABLE', { _uid: 'taskManageTable' })
            }
          })
        })
        .catch(() => {})
    },
    showDetail(row) {
      this.$store.commit('OPEN_DIALOG', {
        component: require('./task-detail.vue').default,
        _uid: 'TaskDetailDialog',
        props: {
          id: row.id,
          taskStatusList: this.taskStatusList,
          datasetList: this.datasetList
        }
      })
    },
  }
}
</script>