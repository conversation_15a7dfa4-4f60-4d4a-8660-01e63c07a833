spring:
  redis:
    database: 3
    timeout: 5000
    host: 127.0.0.1
    port: 6379
#    password: 123456
  #  cluster:
#    nodes: **********:8085
  jackson:
    time-zone: GMT+8
    default-property-inclusion: non_null
    serialization:
      FAIL_ON_EMPTY_BEANS: false
  datasource:
    druid: #以下是全局默认值，可以全局更改
      db-type-current: mysql   # openGauss
      driver-class-name: 1
      #监控统计拦截的filters
      filters: stat
      #配置初始化大小/最小/最大
      initial-size: 1
      min-idle: 1
      max-active: 20
      #获取连接等待超时时间
      max-wait: 60000
      #间隔多久进行一次检测，检测需要关闭的空闲连接
      time-between-eviction-runs-millis: 60000
      #一个连接在池中最小生存的时间
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      #打开PSCache，并指定每个连接上PSCache的大小。oracle设为true，mysql设为false。分库分表较多推荐设置为false
      pool-prepared-statements: false
      max-pool-prepared-statement-per-connection-size: 20
      stat:
        merge-sql: true
        log-slow-sql: true
        slow-sql-millis: 2000
        primary: master
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        master:
          url: **************************************************************************************************************************************
          username: root
          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
          druid:
            validationQuery: select 1

mybatis-plus:
  # 由于升级hos2.5.4版本后，多数据源有问题， 这里手动设置数据库类型
  db-type: ${spring.datasource.druid.db-type-current} # mysql oracle postgresql dm kingbasees,openGauss需要配置成postgresql 支持com.baomidou.mybatisplus.annotation.DbType.java

#安全攻击防护相关配置
framework:

  interface-encryption:
    #是否全局进行接口签名验证,开启之后对全局的数据进行解密加密
    isGlobal: false
    #全局下的加密方式，以及单个接口的默认加密方式，支持的方式有 AES、RSA、国密（SM4）
    decryptType: SM4
    #是否对接口的入参进行解密,全局模式下使用
    isDecrypt: false
    #对body里面的数据单个解密，还是作为一个整体解密
    decryptAll:
      # RequestParam（param参数）的参数进行全参数解密
      params: false
      # RequestBody（body参数）的参数进行全参数解密
      body: false
    # 解密的参数配置，isGlobal为ture(全局解密)，encrypAll为false的情况下，只对单个参数进行解密
    decryptParam:
      # 对请求RequestParam（param参数）中附加的参数进行解密
      params:
        - password
      # 对RequestBody（body参数）里面的参数进行解密,只支持键值对的JSON
      body:
        - password
    #是否对接口返回的结果进行加密，默认情况只加密data字段的数据
    isEncrypt: false
    # RSA公钥,用于加密解密
    publicKey:
    # RSA私钥,用于加密解密
    privateKey:
    # 秘钥，用于接口数据加解密
    secret: 1234567890123456
    # 全局情况下，排除的请求
    excludeUrl:
      - /ceshipost001/*
  security:
    license:
      enable: false
      production:
        name: HOSBase
        version: V2.0.5
    #防xss攻击，默认关闭
    xss:
      enable: false
    #防csrf攻击，默认关闭。此处需要注意：csrf开关开启后，除"GET", "HEAD", "TRACE", "OPTIONS"四类请求可以直接通过外，其他请求都会拦截
    #由于防csrf采用了token机制，所以要求前端页面传入csrfToken，传入方式有两种：
    #方式一：隐藏域  <input type='hidden' name='${_csrf.parameterName}' value='${_csrf.token}'>写法固定；
    #方式二：header中传入，如果token保存在session中（默认），则header-name为 X-CSRF-TOKEN,header-value为token具体值;
    #如果token保存在cookie中，则header-name为 X-XSRF-TOKEN,header-value为token具体值
    csrf:
      enable: false
    #跨域，默认关闭
    cors:
      enable: true
      #allowed:
      #跨域允许来源，可配置多个，在framework.security.cors.enable=true时有效，不配置默认允许所有来源
      #origins: https://www.baidu.com,https://www.taobao.com
      #跨域允许方法，可配置多个，在framework.security.cors.enable=true时有效，不配置默认允许所有方法
      #methods: GET,POST,PUT,DELETE

  # 操作日志输出位置：logFile,database 二者可以同时存在，默认为logFile
  # logFile表示输出到log文件，database表示输出数据库中的sys_oper_log表中
  # 该配置只对OperLog注解生效，与其他日志输出无关
  oper-log:
    out: database

####jwt 相关
jwt:
  # 令牌内容前缀，此处必须修改为自己的
  prefix: mediway_
  # 签名密钥，必须是32位的长度，为了安全，最好设置成自己的，否则会使用默认的
  signKey: mediwaysapowerfulmicroservicearchitectureupgradedandoptimizedfromacommercialproject


#第三方登陆--hos统一认证
social:
  hos:
    codeUrl: ${social.hos.serverUrl}:8329/oauth/authorize
    tokenUrl: ${social.hos.serverUrl}:9086/csm/oauth/token
    userUrl: ${social.hos.serverUrl}:9086/csm/oauth/resource/getUserInfo
    serverUrl: http://114.251.235.9
  enabled: true
  userControl: false
  domain: http://127.0.0.1:8080
  #  domain: http://114.251.235.9:8327
  oauth:
    HOS:
      client-id: 165588O9fa05
      client-secret: 6CDM376v2kOoam86
      redirect-uri: ${social.domain}/oauth/redirect/hos


server:
  port: 9086
  tomcat:
    relaxed-query-chars: <,>,[,]

auth:
  password:
    encode:
      loginPasswordEncode: BCRYPT
      #给下游应用系统同步密码的加密配置，支持SM4;同步账号时用到与开放平台的需要保持一直
      appPasswordEncode: BCRYPT
      #SM4密钥
      appPasswordSecret: 1234567891234567
    user:
      userInfos:
        - loginName: admin
          name: 管理员
          password: $2a$10$BTmUjsvO434oino1eNWehuPOJLzK3qrHyWANE14AQOmW8I9rRr1TK
          id: 2
        - loginName: erpeng
          name: 二碰
          password: $2a$10$BTmUjsvO434oino1eNWehuPOJLzK3qrHyWANE14AQOmW8I9rRr1TK
          id: eca13971fb627b2f75c79475e34ecf07

smscaptcha:
  templateId: 1
  smsSp: rly
  validField: 1
  timeField: 2
pulling:
  config:
    orgsyn:
      interfaceType: restful
      restfulUrl: http://10.1.20.150:8367/api/openApi/sync/getOrgData
      webserviceUrl: http://10.80.99.222/imedical/web/web.SLYTLCPF.HOS.Soap.GetView.CLS
      webserviceMethod: GetView

posyspath: page-office

hos-security:
  login-type: hnzy  # 登录类型： 可选 hnzy
  login:
    white-list: #认证白名单
      - /openApi/**
      - /druid/**
      - /license/*
      - /loginPageData/*
      - /property/is-enable-tenant
      - /tenant/select-tenant-by-domain
      - /tenant/select-tenant
      - /core/system/files/other/**
      - /core/system/files/image/**
      - /websocket/**
      - /favicon.ico
      - /v2/api-docs
      - /swagger-resources
      - /acm/accountAndPWD/validateAccount
      - /doc
      - /**/*.css
      - /**/*.js
      - /**/*.png
      - /**/*.jpg
      - /**/*.jpeg
      - /**/*.html
      - /**/*.ico
      - /**/*.properties
      - /**/*.woff
      - /**/*.woff2
      - /**/*.ttf
      - /hos-job-admin/api/registry
      - /i18n/language/list-select
      - /i18n/element/get-loginPageElements
      - /i18n/config/is-open
      - /i18n/static-translation/transByCode
      - /file/downloadById
      - /org/hos-post/select-post-page
      - /poserver.zz
      - /posetup.exe
      - /sealsetup.exe
      - /poserver.zz
      - /sys/config/is-open-download-browser
      - /login-middle-v1/**
      - /login/passwordFree/userInfo
      - /i18n/element/get-login-page-config
      - /edc/crf-form/design/**
      - /edc/form-maker/form-fill-mobile/disauditable/home-page/**
      - /edc/form-maker/form-fill-mobile/home-page/**
      - /edc/form-maker/form-fill-mobile/patient/fill-form/**
      - /edc/attachment/file/image/view
      - /edc/subject-attachment/file/image/view
      - /edc/subject-attachment/file/download
      - /edc/deform/view/template/all
      - /edc/deform/view/template/page
      - /edc/deform/view/template/detail/**
      - /edc/sync/timestamp/record/cdc/update
      - /openApi/patient/hxey/getAccessToken
      - /search/api/es/**
      - /wechat/content/**
      - /u/**
      - /wx/mp/api/v1/user/bind/**
      - /wx/mp/api/v1/getConfig
      - /wx/route/**
      - /wx/server/**
      - /api/jkl/csm/**
      - /api/sft/csm/**
      - /open/h5/**
      - /**/**

hos:
  server-url: http://localhost:9086/csm #仅为示例。配的当前服务的后端地址（实际项目为代理地址）

oa:
  export:
    # 是否开启OA审核
    enable: false
    # model
    model: webService
    # OA申请地址
    exportApplyUrl: http://************/csp/hsb/DHC.Published.PUB0049.BS.PUB0049.CLS
    # 探索系统下载文件地址
    csmsearchDownloadUrl: http://************:9097/csm-search/open/download/file

csm:
  interface:
    # 适配特殊登录处理
    cas: #cas
    # 是否启用EDC
    edc-enable: false
    # edc
    edc-base-url: http://**********:8090/boot-admin/
    # 数据治理
    datagover-base-url: http://**************:8080/datagover
    # 结构化的接口
    struct-url:
    analyzer-url: http://**********:8089
    analyzer-getdata-url: http://**************:8080/csm-search/api/advanced/analyzerSearch

#数据分析接口签名配置
data-analyzer:
  # csm-platform appID
  app-id: csmPlatform
  # csm-platform 密钥
  app-key: E007a7819Su080M8
  # 数据分析接口地址
  data-analyzer-url: http://localhost:5001
  # 创建数据集API
  create-dataset-api: /api/v1/create/analyzer_dataset
  # 上传导出文件API
  upload-file-api: /api/v1/push/dataset

modelforge:
  parquet:
#    file-path: /Users/<USER>/PycharmProjects/utils/modelforge-parquet-file
    file-path: modelforge-parquet-file
  python:
    base-url: http://127.0.0.1:5003
  output:
    file-path: output