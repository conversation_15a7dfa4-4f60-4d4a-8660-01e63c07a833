<template>
  <div class="training-job-dialog">
    <!-- 步骤条 -->
    <hos-steps 
      simple
      :active="currentStep" 
      align-center
      class="custom-steps clickable-steps">
      <hos-step 
        title="基础信息" 
        description="配置FT服务、基准模型和模型名称"
        icon="hos-icom-sample-stat"
        @click.native="goToStep(0)">
      </hos-step>
      <hos-step 
        title="数据集配置" 
        description="选择训练使用的预置数据集和个人数据集"
        icon="hos-icom-upload-cloud"
        @click.native="goToStep(1)">
      </hos-step>
      <hos-step 
        title="参数配置" 
        description="设置训练方式和相关训练参数"
        icon="hos-icom-edit"
        @click.native="goToStep(2)">
      </hos-step>
    </hos-steps>

    <!-- 第一步：基础信息 -->
    <div v-show="currentStep === 0" class="step-content">
      
      <div class="form-row">
        <div class="form-col">
          <div class="form-label">{{ $t('FT服务') }}</div>
          <hos-select
            v-model="form.ftService"
            :placeholder="$t('请选择FT服务')"
            style="width: 220px"
            @change="handleFtServiceChange"
          >
            <hos-option v-for="item in ftServiceOptions" :key="item.id" :label="item.server" :value="item.id" />
          </hos-select>
        </div>
        <hos-button type="default" style="margin-left: 16px" @click="openFtServerDialog">
          {{ $t('FT服务管理') }}
        </hos-button>

        <hos-button type="primary" style="margin-left: 16px" @click="openGpuUsageDialog">{{
          $t('GPU资源使用情况')
        }}</hos-button>
      </div>
      <div class="form-row">
        <div class="form-col">
          <div class="form-label">{{ $t('基准模型') }}</div>
          <hos-select v-model="form.baseModel" :placeholder="$t('请选择基准模型')" style="width: 220px">
            <hos-option v-for="item in baseModelOptions" :key="item.id" :label="item.model" :value="item.id" />
          </hos-select>
        </div>
      </div>
      <div class="form-row">
        <div class="form-col">
          <div class="form-label">{{ $t('Finetune模型名称') }}</div>
          <hos-input
            v-model="form.finetuneModelName"
            maxlength="50"
            show-word-limit
            :placeholder="$t('请输入Finetune模型名称')"
            style="width: 400px"
          />
        </div>
      </div>
    </div>

    <!-- 第二步：数据集配置 -->
    <div v-show="currentStep === 1" class="step-content">
      <div class="form-row dataset-toolbar">
        <hos-button type="primary" @click="uploadJson">{{ $t('上传个人数据集') }}</hos-button>
        <hos-button type="text" style="margin-left: 8px" @click="downloadJson(sampleData)">{{
          $t('下载示例文件')
        }}</hos-button>
        <div class="custom-sample-box">
          <hos-checkbox v-model="form.customSampleCount">{{ $t('自定义样本数（可选）') }}</hos-checkbox>
          <tips :content="titleTips" :placement="'bottom'" />
        </div>
      </div>
      <div class="dataset-list-section">
        <div class="preset-title">{{ $t('预置数据集') }}</div>
        <hos-table ref="presetDatasetTable" :data="presetDatasetList" border style="width: 100%">
          <hos-table-column type="selection" width="50" />
          <hos-table-column :label="$t('数据集名称')">
            <template #default="scope">
              {{ scope.row.original_name || scope.row.name }}
            </template>
          </hos-table-column>
          <hos-table-column :label="$t('操作')" width="100">
            <template #default="scope">
              <hos-button size="mini" @click="handleDownload(scope.row)">{{ $t('下载') }}</hos-button>
            </template>
          </hos-table-column>
          <hos-table-column prop="sampleCount" :label="$t('样本数')" width="200px">
            <template #default="scope">
              <hos-input-number
                v-if="form.customSampleCount"
                v-model="scope.row.sampleCount"
                :min="1"
                style="width: 160px"
              />
              <span v-else>{{ scope.row.sampleCount }}</span>
            </template>
          </hos-table-column>
        </hos-table>
        <!-- 个人数据集表格 -->
        <div class="preset-title" style="margin-top: 18px">{{ $t('个人数据集') }}</div>
        <hos-table ref="personalDatasetTable" :data="personalDatasetList" border style="width: 100%">
          <hos-table-column type="selection" width="50" />
          <hos-table-column :label="$t('数据集名称')">
            <template #default="scope">
              {{ scope.row.original_name || scope.row.name }}
            </template>
          </hos-table-column>
          <hos-table-column :label="$t('操作')" width="100">
            <template #default="scope">
              <hos-button size="mini" @click="handleDownload(scope.row)">{{ $t('下载') }}</hos-button>
            </template>
          </hos-table-column>
          <hos-table-column prop="sampleCount" :label="$t('样本数')" width="200px">
            <template #default="scope">
              <hos-input-number
                v-if="form.customSampleCount"
                v-model="scope.row.sampleCount"
                :min="1"
                style="width: 160px"
              />
              <span v-else>{{ scope.row.sampleCount }}</span>
            </template>
          </hos-table-column>
        </hos-table>
      </div>
    </div>

    <!-- 第三步：参数配置 -->
    <div v-show="currentStep === 2" class="step-content">
      <!-- 训练方式 -->
      <div class="form-row" style="margin-bottom: 24px">
        <div class="form-col">
          <div class="form-label">{{ $t('训练方式') }}</div>
          <hos-radio-group v-model="form.trainingMethod">
            <hos-radio label="full">{{ $t('全量微调') }}</hos-radio>
            <hos-radio label="freeze">{{ $t('冻结微调') }}</hos-radio>
            <hos-radio label="lora">{{ $t('LoRA微调') }}</hos-radio>
          </hos-radio-group>

          <div class="more-tips" @click="showTrainingMethodTips">说明<i class="hos-icom-tip"></i></div>
        </div>
      </div>
      <!-- 参数配置表格 -->
      <div class="over-params-config">
        <hos-table :data="overParamsConfigDefs" border style="width: 100%">
          <hos-table-column prop="param" :label="$t('参数')" width="210" />
          <hos-table-column :label="$t('数值')" width="200">
            <template #default="scope">
              <hos-input-number
                v-if="scope.row.type === 'number'"
                v-model="form.overParamsConfig[scope.row.param]"
                :min="scope.row.min"
                :max="scope.row.max"
                :step="scope.row.step || 1"
                style="width: 100%"
              />
              <hos-input
                v-else-if="scope.row.type === 'text'"
                v-model="form.overParamsConfig[scope.row.param]"
                style="width: 100%"
              />
              <hos-switch v-else-if="scope.row.type === 'bool'" v-model="form.overParamsConfig[scope.row.param]" />
            </template>
          </hos-table-column>
          <hos-table-column prop="desc" :label="$t('说明')" />
        </hos-table>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer tc">
      <hos-button @click="close">{{ $t('取消') }}</hos-button>
      <hos-button v-if="currentStep > 0" @click="prevStep">{{ $t('上一步') }}</hos-button>
      <hos-button v-if="currentStep < 2" type="primary" @click="nextStep">{{ $t('下一步') }}</hos-button>
      <hos-button type="success" @click="saveTrainingTask">{{ $t('保存') }}</hos-button>
    </div>

    <hos-biz-dialog :title="$t('FT服务器管理')" width="60%" uid="ftSreverDialog" :close-on-click-modal="false" />
    <hos-biz-dialog :title="$t('GPU资源使用情况')" width="60%" uid="gpuUsageDialog" :close-on-click-modal="false" />
    <hos-biz-dialog
      :title="$t('上传文件')"
      uid="uploadSampleJsonDialog"
      width="60%"
      :close-on-click-modal="false"
    ></hos-biz-dialog>
    <hos-biz-dialog
      :title="$t('训练方式说明')"
      uid="trainingMethodTipsDialog"
      class="set-dialog-custom-height"
      width="70%"
      :close-on-click-modal="false"
    ></hos-biz-dialog>
  </div>
</template>

<script>
import sampleData from './sampleData'
export default {
  name: 'TaskDialog',
  // 移除 props: ['visible']
  data() {
    return {
      currentStep: 0, // 当前步骤：0=基础信息，1=数据集配置，2=参数配置
      sampleData,
      titleTips:
        '该项为可选项：<br>若不选中该选项，则选定的个人数据集与预置数据集的所有样本全部参与训练；<br>若选中该选项，则用户可以自由指定不同数据集中参与训练的样本数量，若输入框内空置不填或填写数量大于该数据集总样本，则该数据集全部参与训练。',
      form: {
        ftService: '',
        baseModel: '',
        finetuneModelName: '',
        customSampleCount: false,
        trainingMethod: 'full',
        overParamsConfig: {
          gpus: '',
          val_ratio: 0.1,
          per_device_train_batch_size: 1,
          learning_rate: 0.00005,
          num_train_epochs: 3,
          max_seq_len: 8192,
          cpu_load: false
        }
      },
      ftServiceOptions: [], // FT服务下拉选项
      baseModelOptions: [], // 基准模型下拉选项
      presetDatasetList: [], // 预置数据集
      personalDatasetList: [],
      overParamsConfigDefs: [
        { param: 'gpus', type: 'text', desc: '训练占用的显卡号，多张卡用英文逗号分隔，例如0,1,2,3' },
        {
          param: 'val_ratio',
          type: 'number',
          min: 0,
          max: 1,
          step: 0.01,
          desc: '验证集占比，如果取值大于0，每个epoch结束后会在验证集上算loss'
        },
        {
          param: 'per_device_train_batch_size',
          type: 'number',
          min: 1,
          max: 1024,
          step: 1,
          desc: '批处理大小（BatchSize）表示在每次训练迭代中使用的样本数。较大的批处理大小可以加速训练，但可能会导致显存过大；'
        },
        {
          param: 'learning_rate',
          type: 'number',
          min: 0,
          max: 1,
          step: 0.00001,
          desc: '学习率（LearningRate）是梯度下降的过程中更新权重的超参数，过高会导致模型难以收敛，过低则会导致模型收敛速度过慢；'
        },
        {
          param: 'num_train_epochs',
          type: 'number',
          min: 1,
          max: 100,
          step: 1,
          desc: '迭代次数（epoch），控制训练过程中迭代的轮数；可以根据loss曲线图判断模型是否收敛，如果loss还在下降没有平稳，可以进一步加大epoch'
        },
        {
          param: 'max_seq_len',
          type: 'number',
          min: 512,
          max: 8192,
          step: 1,
          desc: '指定最大序列长度（默认为8192），输入+输出的长度不超过8192，否则会截断处理；'
        },
        {
          param: 'cpu_load',
          type: 'bool',
          desc: '是否将部分参数和优化信息load到cpu上（默认为不开启，显存不够的时候可以开启），开启之后会占用内存，详细的数据可以参考资源消耗相关说明文档'
        }
      ],
      isPublishing: false,
      isCanceling: false,
    }
  },
  created() {
    this.getPresetDatasetList()
    this.getFtServiceOptions()
  },
  mounted() {
    this.getPersonalDatasetList()
  },
  // 移除 watch: { visible ... }
  methods: {
    showTrainingMethodTips() {
      this.$store.commit('OPEN_DIALOG', {
        component: require('./training-method-tips-dialog.vue').default,
        _uid: 'trainingMethodTipsDialog',
        props: {}
      })
    },
    // 步骤切换方法
    nextStep() {
      if (this.currentStep < 2) {
        this.currentStep++
      }
    },
    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },
    // 直接跳转到指定步骤
    goToStep(step) {
      if (step >= 0 && step <= 2) {
        this.currentStep = step
      }
    },
    // 验证所有步骤并返回详细错误信息
    validateAllSteps() {
      const errors = []

      // 第一步：基础信息验证
      if (!this.form.ftService) {
        errors.push('第一步：请选择FT服务')
      }
      if (!this.form.baseModel) {
        errors.push('第一步：请选择基准模型')
      }
      if (!this.form.finetuneModelName || !this.form.finetuneModelName.trim()) {
        errors.push('第一步：请输入Finetune模型名称')
      }

      // 第二步：数据集配置验证
      const selectedPresetDatasets = this.getSelectedPresetDatasets()
      const selectedPersonalDatasets = this.getSelectedPersonalDatasets()
      if (selectedPresetDatasets.length === 0 && selectedPersonalDatasets.length === 0) {
        errors.push('第二步：请至少选择一个数据集（预置数据集或个人数据集）')
      }

      // 第三步：参数配置验证（可以根据需要添加具体验证）
      if (!this.form.trainingMethod) {
        errors.push('第三步：请选择训练方式')
      }

      return errors
    },
    
    openFtServerDialog() {
      this.$store.commit('OPEN_DIALOG', {
        component: require('./ft-server-manage.vue').default, // 假设弹窗组件名称
        _uid: 'ftSreverDialog',
        props: {}
      })
    },
    openGpuUsageDialog() {
      this.$store.commit('OPEN_DIALOG', {
        component: require('./gpu-usage-dialog.vue').default,
        _uid: 'gpuUsageDialog',
        props: {}
      })
    },
    close() {
      // 重置步骤到第一步
      this.currentStep = 0
      this.$store.commit('CLOSE_DIALOG', { _uid: 'TrainingJobDialog' })
    },
    // 验证表单数据

    // 获取选中的预置数据集
    getSelectedPresetDatasets() {
      if (!this.$refs.presetDatasetTable) {
        return []
      }
      const selectedRows = this.$refs.presetDatasetTable.selection || []
      return selectedRows.map((row) => ({
        id: row.id,
        num: this.form.customSampleCount ? row.sampleCount || row.num : row.num,
        url: row.url || row.file_path,
        name: row.name
      }))
    },

    // 获取选中的个人数据集
    getSelectedPersonalDatasets() {
      if (!this.$refs.personalDatasetTable) {
        return []
      }
      const selectedRows = this.$refs.personalDatasetTable.selection || []
      return selectedRows.map((row) => ({
        id: row.id,
        num: this.form.customSampleCount ? row.sampleCount || row.num || 1000 : row.num || 1000,
        url: row.url || row.file_path,
        name: row.name
      }))
    },

    async saveTrainingTask() {
      // 验证所有步骤
      const errors = this.validateAllSteps()
      if (errors.length > 0) {
        // 显示第一个错误，并提示总共有多少个问题
        let errorMessage = errors[0]
        if (errors.length > 1) {
          errorMessage += `（还有${errors.length - 1}个配置问题）`
        }
        this.$message.error(errorMessage)
        return
      }

      try {
        // 获取选中的数据集
        const selectedPresetDatasets = this.getSelectedPresetDatasets()
        const selectedPersonalDatasets = this.getSelectedPersonalDatasets()

        // 构造请求参数
        const payload = {
          server: this.form.ftService,
          base_model: this.form.baseModel,
          model_name: this.form.finetuneModelName.trim(),
          method: this.form.trainingMethod,
          extra_params: {
            gpus: this.form.overParamsConfig.gpus || '',
            val_ratio: this.form.overParamsConfig.val_ratio.toString(),
            per_device_train_batch_size: this.form.overParamsConfig.per_device_train_batch_size.toString(),
            learning_rate: this.form.overParamsConfig.learning_rate.toString(),
            num_train_epochs: this.form.overParamsConfig.num_train_epochs.toString(),
            max_seq_len: this.form.overParamsConfig.max_seq_len.toString(),
            cpu_load: this.form.overParamsConfig.cpu_load.toString()
          },
          train_data: selectedPersonalDatasets,
          preset_data: selectedPresetDatasets
        }

        // 调用创建训练任务接口
        const response = await this.$bsApi('biz.bishengApi.createTrainingTask', payload)

        if (response && response.status_code === 200) {
          this.$message.success('训练任务创建成功')
          // 刷新列表
          this.$store.commit('UPDATE_TABLE', { _uid: 'modelTrainingTable' })
          this.close()
        } else {
          const errorMsg = response && response.status_message ? response.status_message : '创建训练任务失败'
          this.$message.error(errorMsg)
        }
      } catch (error) {
        console.error('创建训练任务失败:', error)
        this.$message.error('创建训练任务失败，请稍后重试')
      }
    },
    async handleDownload(row) {
      try {
        // 调用下载预设数据集接口
        const fileUrl = row.url
        if (!fileUrl) {
          this.$message.error('文件路径不存在')
          return
        }

        // 使用 $bsApi 调用下载接口，返回文件流
        const response = await this.$bsApi('biz.bishengApi.downloadPreDataSet', {
          file_url: fileUrl
        })

        // 直接尝试下载，不判断 response.data.size
          this.downloadFileBlob(response, row.name)
      } catch (error) {
        console.error('下载失败:', error)
        this.$message.error('下载失败，请稍后重试')
      }
    },

    // 处理文件流下载
    downloadFileBlob(response, fallbackFilename) {
      console.log('downloadFileBlob response:', response)
      try {
        // 兼容 headers 大小写和 headers 可能为 undefined 的情况
        const headers = response.headers || {};
        const contentDisposition = headers['content-disposition'] || headers['Content-Disposition'];
        let filename = fallbackFilename || 'download';

        if (contentDisposition) {
          // 解析 Content-Disposition 头中的文件名
          const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
          if (filenameMatch && filenameMatch[1]) {
            filename = filenameMatch[1].replace(/['"]/g, '');
            try {
              filename = decodeURIComponent(filename);
            } catch (e) {
              console.warn('文件名解码失败，使用原始文件名:', filename);
            }
          }
        }

        console.log('下载文件:', filename);

        // 检查 response.data 是否为 Blob
        let blob;
        if (response.data instanceof Blob) {
          blob = response.data;
        } else {
          blob = new Blob([response.data], {
            type: headers['content-type'] || 'application/octet-stream'
          });
        }

        if (!blob || blob.size === 0) {
          this.$message.error('文件下载失败，未获取到文件内容');
          return;
        }

        // 创建下载链接
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.style.display = 'none';

        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 清理 URL 对象
        URL.revokeObjectURL(link.href);

        this.$message.success('文件下载成功');
      } catch (error) {
        console.error('文件下载处理失败:', error);
        this.$message.error('文件下载处理失败');
      }
    },

    // 通用文件下载函数（保留用于其他用途）
    downloadFile(url, filename) {
      console.log('download file :>> ', url)

      // 使用axios下载文件
      const axios = require('axios')
      axios
        .get(url, { responseType: 'blob' })
        .then((res) => {
          const blob = new Blob([res.data])
          const link = document.createElement('a')
          link.href = URL.createObjectURL(blob)
          link.download = filename || 'download'
          link.click()
          URL.revokeObjectURL(link.href)
        })
        .catch((error) => {
          console.error('文件下载失败:', error)
          this.$message.error('文件下载失败')
        })
    },
    handleDeletePersonalDataset(index) {
      this.$confirm('确定要删除这个个人数据集吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.personalDatasetList.splice(index, 1)
          this.$message.success('删除成功')
        })
        .catch(() => {
          // 用户取消删除
        })
    },
    getPresetDatasetList() {
      this.$bsApi('biz.bishengApi.queryPreDataSet').then((res) => {
        if (res && res.data && res.data.list) {
          this.presetDatasetList = res.data.list
        } else {
          this.presetDatasetList = []
        }
      })
    },
    getFtServiceOptions() {
      this.$bsApi('biz.bishengApi.queryFtList').then((res) => {
        if (res && res.data && Array.isArray(res.data)) {
          this.ftServiceOptions = res.data
        } else {
          this.ftServiceOptions = []
        }
      })
    },

    handleFtServiceChange(serverId) {
      // const testData = {
      //   status_code: 200,
      //   status_message: 'SUCCESS',
      //   data: [
      //     {
      //       id: 1,
      //       endpoint: 'http://127.0.0.1:8000/v2.1/models',
      //       server: '1',
      //       model: 'my-model-1',
      //       config: null,
      //       status: null,
      //       remark: null,
      //       create_time: '2024-06-01T12:00:00',
      //       update_time: '2024-06-01T12:00:00',
      //       sft_support: true
      //     },
      //     {
      //       id: 2,
      //       endpoint: 'http://127.0.0.1:8000/v2.1/models',
      //       server: '1',
      //       model: 'my-model-2',
      //       config: null,
      //       status: null,
      //       remark: null,
      //       create_time: '2024-06-02T12:00:00',
      //       update_time: '2024-06-02T12:00:00',
      //       sft_support: true
      //     }
      //   ]
      // }

      this.form.baseModel = ''
      if (!serverId) {
        this.baseModelOptions = []
        return
      }
      this.$bsApi('biz.bishengApi.queryBaseModel', { server_id: serverId }).then((res) => {
        if (res && res.data && Array.isArray(res.data)) {
          this.baseModelOptions = res.data
        } else {
          this.baseModelOptions = []
        }
      })
    },
    afterRightUpload(uploadResult) {
      // 文件上传成功后，将返回的数据添加到个人数据集列表中
      if (uploadResult && Array.isArray(uploadResult)) {
        // 处理返回的数据数组
        uploadResult.forEach((item) => {
          // 添加默认样本数，如果没有的话
          const datasetItem = {
            ...item,
            sampleCount: item.sampleCount || item.num || 1000 // 默认样本数
          }
          // 检查是否已存在相同id的数据，避免重复添加
          const existingIndex = this.personalDatasetList.findIndex((existing) => existing.id === item.id)
          if (existingIndex === -1) {
            this.personalDatasetList.push(datasetItem)
          } else {
            // 如果已存在，更新数据
            this.personalDatasetList.splice(existingIndex, 1, datasetItem)
          }
        })
      } else if (uploadResult) {
        // 处理单个数据对象
        const datasetItem = {
          ...uploadResult,
          sampleCount: uploadResult.sampleCount || uploadResult.num || 1000
        }
        const existingIndex = this.personalDatasetList.findIndex((existing) => existing.id === uploadResult.id)
        if (existingIndex === -1) {
          this.personalDatasetList.push(datasetItem)
        } else {
          this.personalDatasetList.splice(existingIndex, 1, datasetItem)
        }
      }
    },
    downloadJson(content) {
      const jsonStr = JSON.stringify(content)
      const jsonString = `data:text/json;chatset=utf-8,${encodeURIComponent(jsonStr)}`

      const link = document.createElement('a')
      link.href = jsonString
      link.download = `sample.json`

      link.click()
    },
    uploadJson() {
      const downloadTemplateOption = {
        uploadTypeState: false, // 是否需要选择导入数据类型,目前只有新增场景,后续看接口适配情况
        isHide: true, // 是否隐藏下载
        method: 'GET', // 请求方式
        apiUrl: '', // 下载模板Api对应的接口地址
        params: {}, // 拼到请求路径上的参数
        data: {}, // body参数 POST请求时用到
        templateFileName: ''
      }
      const uploadFileOption = {
        uploadTypeState: false, // 是否需要选择导入数据类型,目前只有新增场景,后续看接口适配情况
        method: 'post',
        apiUrl: 'api/v1/finetune/job/file',
        params: {},
        data: {
          // versionId: versionId
        }
      }

      this.$store.commit('OPEN_DIALOG', {
        component: require('./file-down-upload.vue').default,
        _uid: 'uploadSampleJsonDialog',
        props: {
          downloadTemplateOption: downloadTemplateOption,
          uploadFileOption: uploadFileOption,
          afterUpload: this.afterRightUpload
        }
      })
    },
    async handlePublish() {
      this.isPublishing = true
      try {
        const res = await this.$bsApi('biz.bishengApi.publishJob', { job_id: this.jobId })
        this.$message.success('发布任务已提交，正在后台处理中，请稍后刷新页面查看状态')
        // 3秒后自动刷新任务状态
        setTimeout(() => this.fetchJobStatus && this.fetchJobStatus(), 3000)
      } catch (e) {
        this.$message.error('发布失败，请稍后重试')
      } finally {
        this.isPublishing = false
      }
    },
    async handleCancelPublish() {
      this.isCanceling = true
      try {
        const res = await this.$bsApi('biz.bishengApi.cancelPublishJob', { job_id: this.jobId })
        this.$message.success('取消发布任务已提交，正在后台处理中，请稍后刷新页面查看状态')
        setTimeout(() => this.fetchJobStatus && this.fetchJobStatus(), 3000)
      } catch (e) {
        this.$message.error('取消发布失败，请稍后重试')
      } finally {
        this.isCanceling = false
      }
    },
    // 任务状态刷新函数（如已有可复用，否则留空）
    fetchJobStatus() {
      // 获取任务详情并刷新本地状态
      if (!this.jobId) return
      this.$bsApi('biz.bishengApi.getJobInfo', { job_id: this.jobId }).then(res => {
        if (res && res.status_code === 200 && res.data) {
          // 假设你有 detailData 或类似变量存储任务详情
          this.detailData = res.data.finetune || res.data
        }
      })
    },
    async getPersonalDatasetList() {
      try {
        const res = await this.$bsApi('biz.bishengApi.queryPersonalDatasetList')
        if (res && res.data && Array.isArray(res.data.list)) {
          this.personalDatasetList = res.data.list.map(item => ({
            ...item,
            sampleCount: item.sampleCount || item.num || 1000
          }))
        } else {
          this.personalDatasetList = []
        }
      } catch (e) {
        this.personalDatasetList = []
      }
    },
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      const pad = n => n.toString().padStart(2, '0');
      const yyyy = d.getFullYear();
      const mm = pad(d.getMonth() + 1);
      const dd = pad(d.getDate());
      const hh = pad(d.getHours());
      const min = pad(d.getMinutes());
      const ss = pad(d.getSeconds());
      return `${yyyy}-${mm}-${dd} ${hh}:${min}:${ss}`;
    }
  }
}
</script>

<style lang="scss" scoped>
.training-job-dialog {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-width: 800px;
  padding: 0;

  .form-row {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
  }
  .form-col {
    display: flex;
    align-items: center;
    .form-label {
      min-width: 120px;
      font-size: 15px;
      color: #333;
      margin-right: 8px;
    }

    .more-tips {
      margin-left:30px;
      color:#509CFC;
      cursor:pointer;
      position: relative;
      top: -2px;

      i {
        margin-left: 2px;
      }
    }
  }
  .dataset-list-section {
    margin-top: 18px;
    .preset-title {
      font-size: 15px;
      font-weight: 500;
      margin-bottom: 10px;
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: center;
    padding-bottom: 16px;
    gap: 16px;
  }
}
.ft-add-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.custom-sample-box {
  display: flex;
  align-items: center;
  margin-left: auto;
  .el-icon-question {
    margin-left: 4px;
    color: #999;
    cursor: pointer;
  }
}
.dataset-toolbar {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  .custom-sample-box {
    margin-left: auto;
    display: flex;
    align-items: center;
  }
}
.no-personal-data {
  color: #999;
  padding: 16px 0 0 60px;
}

/* 步骤条样式优化 */
.training-job-dialog {
  .custom-steps {
    padding-left: 16% !important;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    
    ::v-deep .hos-step__icon {
      width: 48px !important;
      height: 48px !important;
      font-size: 24px !important;
      
      i {
        font-size: 24px !important;
      }
    }
    
    ::v-deep .hos-step__title {
      font-size: 16px !important;
      font-weight: 600 !important;
    }
    
    ::v-deep .hos-step__description {
      font-size: 14px !important;
      color: #666 !important;
      margin-top: 8px !important;
    }
  }

  /* 可点击步骤条样式 */
  .clickable-steps {
    ::v-deep .hos-step {
      cursor: pointer;
    }
  }
}

.step-content {
  height: calc(100% - 150px);
  overflow-y: auto;
  padding: 24px 32px;
}

.dialog-footer {
  padding: 15px 32px;
  border-top: 1px solid #eee;
  background-color: #fff;
  
  .hos-button {
    margin-left: 10px;
  }
}
</style>