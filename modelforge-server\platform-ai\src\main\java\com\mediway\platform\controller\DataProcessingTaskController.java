package com.mediway.platform.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mediway.hos.base.model.BaseResponse;
import com.mediway.platform.model.dto.DataProcessingTaskDTO;
import com.mediway.platform.model.entity.DataProcessingTaskEntity;
import com.mediway.platform.model.vo.DataProcessingTaskVO;
import com.mediway.platform.service.DataProcessingTaskService;
import com.mediway.platform.utils.ConvertUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据处理任务管理控制器，处理 HTTP 请求并调用服务层
 */
@Api(tags = "数据处理任务管理")
@RestController
@RequestMapping("/ai/data-processing-task")
public class DataProcessingTaskController {
    private static final Logger logger = LoggerFactory.getLogger(DataProcessingTaskController.class);

    @Autowired
    private DataProcessingTaskService taskService;

    /**
     * 创建数据处理任务
     * @param dto 任务 DTO
     * @return 操作结果响应
     */
    @ApiOperation("创建数据处理任务")
    @PostMapping("/create")
    public BaseResponse createTask(@Validated @RequestBody DataProcessingTaskDTO dto) {
        try {
            taskService.createTask(dto); // 调用服务层创建任务
            return BaseResponse.success();
        } catch (Exception e) {
            logger.error("创建数据处理任务时发生异常", e);
            return BaseResponse.error("创建数据处理任务时发生异常");
        }
    }

    /**
     * 更新数据处理任务
     * @param dto 任务 DTO
     * @return 操作结果响应
     */
    @ApiOperation("更新数据处理任务")
    @PostMapping("/update")
    public BaseResponse updateTask(@Validated @RequestBody DataProcessingTaskDTO dto) {
        try {
            DataProcessingTaskEntity entity = ConvertUtils.convertToEntity(dto, DataProcessingTaskEntity.class);
            boolean success = taskService.updateTask(entity); // 调用服务层更新任务
            if (success) {
                return BaseResponse.success();
            } else {
                logger.error("更新数据处理任务失败，DTO: {}", dto);
                return BaseResponse.error("更新数据处理任务失败");
            }
        } catch (Exception e) {
            logger.error("更新数据处理任务时发生异常", e);
            return BaseResponse.error("更新数据处理任务时发生异常");
        }
    }

    @ApiOperation("执行数据处理任务")
    @PostMapping("/start")
    public BaseResponse startTask(@Validated @RequestBody DataProcessingTaskDTO dto) {
        try {
            DataProcessingTaskEntity entity = ConvertUtils.convertToEntity(dto, DataProcessingTaskEntity.class);
            boolean success = taskService.updateTask(entity); // 调用服务层更新任务



            if (success) {
                return BaseResponse.success();
            } else {
                logger.error("更新数据处理任务失败，DTO: {}", dto);
                return BaseResponse.error("更新数据处理任务失败");
            }
        } catch (Exception e) {
            logger.error("更新数据处理任务时发生异常", e);
            return BaseResponse.error("更新数据处理任务时发生异常");
        }
    }

    /**
     * 删除数据处理任务
     * @param id 任务 ID
     * @return 操作结果响应
     */
    @ApiOperation("删除数据处理任务")
    @PostMapping("/delete/{id}")
    public BaseResponse deleteTask(@PathVariable String id) {
        try {
            boolean success = taskService.deleteTask(id); // 调用服务层删除任务
            if (success) {
                return BaseResponse.success();
            } else {
                logger.error("删除数据处理任务失败，ID: {}", id);
                return BaseResponse.error("删除数据处理任务失败");
            }
        } catch (Exception e) {
            logger.error("删除数据处理任务时发生异常，ID: {}", id, e);
            return BaseResponse.error("删除数据处理任务时发生异常");
        }
    }

    /**
     * 获取任务详情
     * @param id 任务 ID
     * @return 任务详情响应
     */
    @ApiOperation("获取任务详情")
    @GetMapping("/detail/{id}")
    public BaseResponse getTaskById(@PathVariable String id) {
        try {
            DataProcessingTaskVO vo = taskService.getTaskById(id); // 调用服务层获取任务详情
            if (vo == null) {
                return BaseResponse.error("数据处理任务不存在");
            }
            return BaseResponse.success(vo);
        } catch (Exception e) {
            logger.error("获取数据处理任务详情时发生异常，ID: {}", id, e);
            return BaseResponse.error("获取数据处理任务详情时发生异常");
        }
    }

    /**
     * 获取所有数据处理任务
     * @return 任务列表响应
     */
    @ApiOperation("获取所有数据处理任务")
    @GetMapping("/list")
    public BaseResponse getAllTasks() {
        try {
            List<DataProcessingTaskVO> vos = taskService.getAllTasks(); // 调用服务层获取所有任务
            return BaseResponse.success(vos);
        } catch (Exception e) {
            logger.error("获取所有数据处理任务时发生异常", e);
            return BaseResponse.error("获取所有数据处理任务时发生异常");
        }
    }

    /**
     * 分页查询数据处理任务
     * @param current 当前页码
     * @param size 每页记录数
     * @param taskName 任务名称（可选）
     * @param datasetId 数据集 ID（可选）
     * @param taskStatus 任务状态（可选）
     * @return 分页结果响应
     */
    @ApiOperation("分页查询数据处理任务")
    @GetMapping("/page")
    public BaseResponse getTaskPage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String taskName,
            @RequestParam(required = false) String datasetId,
            @RequestParam(required = false) String taskStatus) {
        try {
            IPage<DataProcessingTaskVO> voPage = taskService.getTaskPage(current, size, taskName, datasetId, taskStatus);
            return BaseResponse.success(voPage);
        } catch (Exception e) {
            logger.error("分页查询数据处理任务时发生异常，参数：pageNum={}, pageSize={}, taskName={}, datasetId={}, taskStatus={}",
                    current, size, taskName, datasetId, taskStatus, e);
            return BaseResponse.error("分页查询数据处理任务时发生异常");
        }
    }

    /**
     * 批量删除数据处理任务
     * @param ids 任务 ID 列表
     * @return 操作结果响应
     */
    @ApiOperation("批量删除数据处理任务")
    @PostMapping("/deletion")
    public BaseResponse batchDeleteTasks(@RequestBody List<String> ids) {
        try {
            boolean success = taskService.batchDeleteTasks(ids); // 调用服务层批量删除
            if (success) {
                return BaseResponse.success();
            } else {
                logger.error("批量删除数据处理任务失败，IDs: {}", ids);
                return BaseResponse.error("批量删除数据处理任务失败");
            }
        } catch (Exception e) {
            logger.error("批量删除数据处理任务时发生异常，IDs: {}", ids, e);
            return BaseResponse.error("批量删除数据处理任务时发生异常");
        }
    }
}