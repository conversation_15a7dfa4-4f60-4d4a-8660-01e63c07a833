<template>
  <div class="h-fit dataset-detail-warapper">
    <div class="dataset-info-topbar">
      <div class="name">{{ datasetInfo.name }}</div>
      <div class="type">{{ listToMap(this.typeList)[datasetInfo.type] }}</div>
      <div class="data-usage">{{ listToMap(this.dataUsageList)[datasetInfo.dataUsage] }}</div>
      <div class="create-time">
        <span class="label">{{ $t('创建时间') }}：</span>
        <span class="time">{{ datasetInfo.createTime }}</span>
      </div>
    </div>
    <div class="content">
      <hos-aside class="dataset-tabs-aside h-fit">
        <hos-biz-table
          uid="leftDatasetVersionTable"
          ref="leftDatasetVersionTable"
          :cols="leftCols"
          :form="leftForm"
          :data="leftSelectPage"
          @after-load="afterLoad"
          @row-click="handlePostClick"
          :highlight-current-row="highlightStatus"
          :row-class-name="tableRowClassName"
          :rowDisabledMethod="rowDisabledMethod"
        >
          <template #form>
            <hos-row :gutter="20">
              <hos-col :span="18">
                <hos-form-item label-width="0">
                  <hos-input
                    v-model="leftForm.model.versionDescription"
                    clearable
                    :placeholder="$t('版本说明')"
                  ></hos-input>
                </hos-form-item>
              </hos-col>
              <hos-col :span="6">
                <hos-form-item label-width="0">
                  <hos-biz-button run="form.search" type="primary">{{ $t('查询') }}</hos-biz-button>
                </hos-form-item>
              </hos-col>
            </hos-row>
          </template>
          <template #toolbar>
            <hos-button-group class="hos">
              <hos-button
                @click="addVersionDialog()"
                icon="hos-icom-add"
                v-has-permi="{
                  key: ''
                }"
                >{{ $t('新增') }}</hos-button
              >
            </hos-button-group>
          </template>
          <template #operation="{ row }">
            <hos-tooltip class="pr10" :content="$t('查看')" v-has-permi="{ key: '' }">
              <i class="hos-icom-eye" @click="viewLeftRow(row)"></i>
            </hos-tooltip>
            <hos-tooltip class="pr10" :content="$t('删除')" v-has-permi="{ key: '' }">
              <i class="hos-icom-cancel" @click="deleteRow(row)"></i>
            </hos-tooltip>
          </template>
        </hos-biz-table>
      </hos-aside>

      <hos-main class="h-fit dataset-main">
        <hos-biz-table
          uid="rightVersionDetailTable"
          ref="rightVersionDetailTable"
          :cols="rightCols"
          :form="rightForm"
          :data="rightSelectPage"
          :init="false"
        >
          <template #form>
            <hos-row :gutter="20">
              <!-- <hos-col :span="4">
                <hos-form-item>
                  <hos-input v-model="rightForm.model.search" clearable :placeholder="$t('对话内容')"></hos-input>
                </hos-form-item>
              </hos-col>
              <hos-col :span="4">
                <hos-form-item label-width="0">
                  <hos-biz-button type="primary" @click="handleSearch">{{ $t('查询') }}</hos-biz-button>
                </hos-form-item>
              </hos-col> -->

              <hos-col v-if="!versionIsActive" :span="24">
                <hos-form-item label-width="0">
                  <hos-biz-button type="primary" style="float: right;" @click="handlePublish">{{ $t('发布') }}</hos-biz-button>
                </hos-form-item>
              </hos-col>
              <hos-col v-else :span="24">
                <hos-form-item label-width="0">
                  <hos-biz-button type="primary" style="float: right;" @click="handleUnpublish">{{ $t('取消发布') }}</hos-biz-button>
                </hos-form-item>
              </hos-col>
            </hos-row>
          </template>
          <template #toolbar>
            <hos-button-group class="hos">
              <hos-button icon="hos-icom-import" @click="uploadExcel()" v-has-permi="{ key: '' }">{{
                $t('导入')
              }}</hos-button>
              <div class="hos-down" style="position: relative; top: -2px">
                <hos-dropdown trigger="click" @command="exportData">
                  <span class="hos-dropdown-link">
                    <i class="hos-icom-export" style="padding: 0 8px 0 5px"></i>{{ $t('导出')
                    }}<i class="hos-icon-arrow-down hos-icon--right"></i>
                  </span>
                  <hos-dropdown-menu slot="dropdown">
                    <hos-dropdown-item :command="3">{{ $t('导出EXCEL') }}</hos-dropdown-item>
                    <hos-dropdown-item :command="2">{{ $t('导出JSON') }}</hos-dropdown-item>
                    <hos-dropdown-item :command="1">{{ $t('导出CSV') }}</hos-dropdown-item>
                    <hos-dropdown-item :command="4">{{ $t('导出TXT') }}</hos-dropdown-item>
                  </hos-dropdown-menu>
                </hos-dropdown>
              </div>
            </hos-button-group>
          </template>
          <template #history="{ row }">
            <div v-if="formatHistory(row.history).length > 0" class="history-content">
              <div 
                v-for="(item, index) in formatHistory(row.history)" 
                :key="index" 
                class="history-item"
              >
                <span class="history-label">Q:</span>
                <span class="history-text">{{ item[0] }}</span>
                <br />
                <span class="history-label">A:</span>
                <span class="history-text">{{ item[1] }}</span>
                <hr v-if="index < formatHistory(row.history).length - 1" class="history-divider" />
              </div>
            </div>
            <span v-else class="empty-history">-</span>
          </template>
          
          <template #operation="{ row }">
            <hos-tooltip class="pr10" :content="$t('查看')" v-has-permi="{ key: '' }">
              <i class="hos-icom-eye" @click="viewRightRow(row)"></i>
            </hos-tooltip>
          </template>
        </hos-biz-table>
      </hos-main>
    </div>
    <hos-biz-dialog
      :title="$t('新增版本')"
      uid="addVersionDialog"
      width="60%"
      :close-on-click-modal="false"
    ></hos-biz-dialog>

    <hos-biz-dialog
      :title="$t('详情')"
      uid="conversationDetailDialog"
      width="50%"
      :close-on-click-modal="false"
    ></hos-biz-dialog>

    <hos-biz-dialog
      :title="$t('导入数据')"
      uid="importContentDialog"
      width="60%"
      :close-on-click-modal="false"
    ></hos-biz-dialog>
  </div>
</template>

<script>
import { returnGlobalValue } from '@base/utils'
import { getToken } from '@base/utils/base/token-util'
import axios from 'axios'
import Vue from 'vue'
import { getLocale } from '@base/utils/i18n/i18n-util'
import UserConstant from '@base/constant/user-constant'
export default {
  data() {
    return {
      typeList: [],
      dataUsageList: [],
      versionList: [],
      datasetId: '',
      datasetInfo: {},
      currentVersionId: '',
      leftForm: {
        labelWidth: 'auto',
        model: {
          versionDescription: ''
        }
      },
      rightForm: {
        model: {
          datasetId: '',
          search: ''
        }
      },
      leftCols: [
        {
          prop: 'versionNumber',
          minWidth: '80px',
          label: this.$t('版本号')
        },
        {
          prop: 'versionDescription',
          minWidth: '190px',
          label: this.$t('版本说明')
        },
        {
          label: this.$t('操作'),
          width: '70px',
          prop: 'operation',
          slotName: 'operation'
        }
      ],
      highlightStatus: true,
      lastVersionNum: 0
    }
  },
  created() {
    this.getDictItemByCode('datasets-type', 'typeList')
    this.getDictItemByCode('data-usage', 'dataUsageList')

    this.datasetId = this.$route.query.datasetId || ''
    // 根据数据集id获取数据集信息,topbar展示信息用
    this.$api('biz.datasetsManage.queryDetailApi', this.datasetId).then((res) => {
      console.log(res, '31')
      this.datasetInfo = res.data
    })
  },
  methods: {
    handleSearch() {
      // 检查参数是否为空
      if (!this.currentVersionId) {
        // 参数为空，显示提示信息
        this.$message.warning('请先选择数据集版本！')
      } else {
        this.$store.commit('UPDATE_TABLE', { _uid: 'rightVersionDetailTable' })
      }
    },
    
    // 发布版本
    handlePublish() {
      this.updateVersionStatus(1, '发布')
    },
    
    // 取消发布版本
    handleUnpublish() {
      this.updateVersionStatus(0, '取消发布')
    },
    
    // 更新版本发布状态的通用方法
    updateVersionStatus(status, actionName) {
      // 检查参数是否为空
      if (!this.currentVersionId) {
        this.$message.warning('请先选择数据集版本！')
        return
      }

      // 确认操作
      this.$confirm(`确定要${actionName}当前数据集版本吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 调用API更新版本发布状态
          const updateData = {
            id: this.currentVersionId,
            isActivity: status
          }
          
          this.$api('biz.datasetVersion.editApi', updateData).then((res) => {
            if (res.code == 200) {
              this.$message.success(`${actionName}成功`)
              
              // 更新左侧版本列表（afterLoad会自动还原选中状态）
              this.$store.commit('UPDATE_TABLE', { _uid: 'leftDatasetVersionTable' })
            } else {
              this.$message.error(res.msg || `${actionName}失败`)
            }
          }).catch(error => {
            console.error(`${actionName}失败:`, error)
            this.$message.error(`${actionName}失败，请稍后重试`)
          })
        })
        .catch(() => {
          // 用户取消操作
        })
    },
    //检索左侧的页面
    leftSelectPage(params) {
      params.datasetId = this.datasetId
      return this.$api('biz.datasetVersion.queryPageApi', params)
    },
    rightSelectPage(params) {
      if (!this.currentVersionId) return false
      
      // 临时返回假数据用于展示效果
      // const mockData = {
      //   code: "200",
      //   msg: "操作成功",
      //   data: {
      //     records: [
      //       {
      //         id: "1949743482675994625",
      //         instruction: "查询天气",
      //         input: "北京",
      //         output: "北京今天晴天，气温25-32℃，空气质量良。",
      //         history: "[]"
      //       },
      //       {
      //         id: "1949743482772463618",
      //         instruction: "翻译成英文",
      //         input: "你好，世界",
      //         output: "Hello, world",
      //         history: "[[\"打开翻译模式\",\"翻译模式已激活\"]]"
      //       },
      //       {
      //         id: "1949743482772463619",
      //         instruction: "计算数学题",
      //         input: "125×48",
      //         output: "125×48=6000",
      //         history: "[[\"我需要计算器\",\"计算器功能已就绪\"],[\"先算100×50\",\"100×50=5000\"]]"
      //       },
      //       {
      //         id: "1949743482772463620",
      //         instruction: "推荐电影",
      //         input: "科幻类型",
      //         output: "推荐《星际穿越》《盗梦空间》《阿凡达》三部科幻电影。",
      //         history: "[]"
      //       },
      //       {
      //         id: "1949743482772463621",
      //         instruction: "解释术语",
      //         input: "人工智能",
      //         output: "人工智能是模拟人类智能的计算机系统，能执行学习、推理、决策等任务。",
      //         history: "[[\"什么是AI\",\"AI是人工智能的英文缩写\"]]"
      //       },
      //       {
      //         id: "1949743482772463622",
      //         instruction: "生成诗歌",
      //         input: "关于春天的五言诗",
      //         output: "春风吹又生，花开满园香。鸟语枝头闹，人间好时光。",
      //         history: "[]"
      //       },
      //       {
      //         id: "1949743482772463623",
      //         instruction: "设置提醒",
      //         input: "明天下午3点开会",
      //         output: "已设置提醒：明天15:00 会议提醒",
      //         history: "[[\"打开日历\",\"日历功能已激活\"]]"
      //       },
      //       {
      //         id: "1949743482772463624",
      //         instruction: "查询航班",
      //         input: "上海到纽约 9月1日",
      //         output: "9月1日有3班航班：MU587(08:20)、AA182(12:45)、DL388(15:30)",
      //         history: "[]"
      //       },
      //       {
      //         id: "1949743482772463625",
      //         instruction: "健康建议",
      //         input: "经常失眠怎么办",
      //         output: "建议：1.保持规律作息 2.睡前避免蓝光 3.适量运动 4.可尝试冥想放松",
      //         history: "[[\"打开健康助手\",\"健康咨询模式已启动\"]]"
      //       },
      //       {
      //         id: "1949743482772463626",
      //         instruction: "讲个笑话",
      //         input: "",
      //         output: "程序员最讨厌的节日是什么？劳动节和国庆节，因为要劳动和加班！",
      //         history: "[]"
      //       }
      //     ],
      //     total: 26,
      //     size: 10,
      //     current: 1,
      //     orders: [],
      //     optimizeCountSql: true,
      //     searchCount: true,
      //     pages: 3
      //   },
      //   success: true
      // }
      
      // return Promise.resolve(mockData)

      
      // 获取当前选中版本的信息
      const currentVersion = this.$refs.leftDatasetVersionTable.tableData.find(
        (item) => item.id === this.currentVersionId
      )
      
      if (!currentVersion) return false
      
      // 设置新的分页接口参数
      const requestParams = {
        current: params.current || 1,
        size: params.size || 10, 
        datasetId: this.datasetId,
        version: currentVersion.versionNumber
      }
      
      // 如果有搜索条件，添加搜索参数
      if (this.rightForm.model.search) {
        requestParams.search = this.rightForm.model.search
      }
      
      return this.$api('biz.datasetVersion.queryFileDetailApi', requestParams)
      
    },
    afterLoad() {
      console.log(this.$refs.leftDatasetVersionTable, '112')
      if (this.$refs.leftDatasetVersionTable.tableData.length > 0) {
        let targetRow = null
        
        // 如果currentVersionId存在，尝试找到对应的版本
        if (this.currentVersionId) {
          targetRow = this.$refs.leftDatasetVersionTable.tableData.find(item => item.id === this.currentVersionId)
        }
        
        // 如果没找到对应版本，默认选中第一行
        if (!targetRow) {
          targetRow = this.$refs.leftDatasetVersionTable.tableData[0]
          this.currentVersionId = targetRow.id
        }
        
        this.$refs.leftDatasetVersionTable.setCurrentRow(targetRow)
        this.$store.commit('UPDATE_TABLE', { _uid: 'rightVersionDetailTable' })
      } else {
        this.currentVersionId = ''
        this.$refs.rightVersionDetailTable.tableData = []
      }
    },
    handlePostClick(row) {
      this.highlightStatus = true
      this.currentVersionId = row.id
      this.$store.commit('UPDATE_TABLE', { _uid: 'rightVersionDetailTable' })
    },
    tableRowClassName({ row }) {
      if (row.disabled) {
        return 'table-row-not-select'
      }
    },
    rowDisabledMethod(row) {
      return row.disabled === true
    },
    getVersionListAndSetLastVersion() {
      return this.$api('biz.datasetVersion.queryListApi', { datasetId: this.datasetId })
    },
    deleteRow(row) {
      this.$confirm(
        this.$t('是否删除【{0}】？将同时删除版本下的数据').replace('{0}', row.versionNumber),
        this.$t('删除'),
        {
          type: 'error'
        }
      ).then(() => {
        if (row) {
          // 如果左侧表格只有一条数据，清空当前选中的文件id
          if (this.$refs.leftDatasetVersionTable.tableData.length == 1) {
            this.currentVersionId = ''
          }
          this.$api('biz.datasetVersion.deleteApi', {
            id: row.id,
            deleteData: 1
          }).then((res) => {
            if (res && res.code == '200') {
              this.$message.success(res.msg)
              // 更新左边表格
              this.$store.commit('UPDATE_TABLE', { _uid: 'leftDatasetVersionTable' })
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    addVersionDialog() {
      // 确保新增时拿到的是最新的版本列表数据
      this.getVersionListAndSetLastVersion().then((res) => {
        this.versionList = res.data || []

        if (this.versionList.length > 0) {
          // 根据当前版本列表最后一个的版本号，递增1，新增默认这个名字
          const lastVersionStr = this.versionList[this.versionList.length - 1].versionNumber
          if (lastVersionStr == 'BASE') {
             this.lastVersionNum = 0
          } else {
            this.lastVersionNum = parseInt(lastVersionStr.split('V')[1])
          }
        } else {
          this.lastVersionNum = 0
        }

        this.$store.commit('OPEN_DIALOG', {
          component: require('./add-version-dialog.vue').default,
          _uid: 'addVersionDialog',
          props: {
            versionList: this.versionList,
            lastVersionNum: this.lastVersionNum,
            datasetId: this.datasetId,
            datasetType: this.datasetInfo.type
          }
        })
      })
    },
    viewLeftRow(row) {
      this.currentVersionId = row.id
      // 触发右侧树更新
      this.$store.commit('UPDATE_TABLE', { _uid: 'rightVersionDetailTable' })
    },
    viewRightRow(row) {
      this.$store.commit('OPEN_DIALOG', {
        component: require('./conversation-detail-dialog').default,
        _uid: 'conversationDetailDialog',
        props: {
          fileId: row.fileId,
          row: row,
          rightCols: this.rightCols
        }
      })
    },
    uploadExcel() {
      const downloadTemplateOption = {
        uploadTypeState: false, // 是否需要选择导入数据类型,目前只有新增场景,后续看接口适配情况
        isHide: false, // 是否隐藏下载
        method: 'GET', // 请求方式
        apiUrl: 'ai/dataset/downloadFile', // 下载模板Api对应的接口地址
        params: {
          type: this.datasetInfo.type
        }, // 拼到请求路径上的参数
        data: {}, // body参数 POST请求时用到
        templateFileName: this.datasetInfo.type == 2 ? this.$t('测评集导入模板') : this.$t('训练集导入模板')
      }

      const versionId = this.$refs.leftDatasetVersionTable.tableData.find(
        (item) => item.id === this.currentVersionId
      )?.id
      const uploadFileOption = {
        uploadTypeState: false, // 是否需要选择导入数据类型,目前只有新增场景,后续看接口适配情况
        method: 'post',
        apiUrl: 'ai/dataset/version/import',
        params: {},
        data: {
          versionId: versionId
        }
      }

      this.$store.commit('OPEN_DIALOG', {
        component: require('../datasets-manage/file-down-upload.vue').default,
        _uid: 'importContentDialog',
        props: {
          downloadTemplateOption: downloadTemplateOption,
          afterUpload: this.afterRightUpload,
          uploadFileOption: uploadFileOption
        }
      })
    },
    // 上传完成后，更新列表，关闭弹窗

    afterRightUpload() {
      this.handleSearch()
      this.$store.commit('CLOSE_DIALOG', {
        _uid: 'importContentDialog'
      })
    },
    exportData(command) {
      const fileType = command || 3
      const versionId = this.$refs.leftDatasetVersionTable.tableData.find(
        (item) => item.id === this.currentVersionId
      )?.id
      let url = `${returnGlobalValue(
        'VUE_APP_BASE_URL'
      )}/ai/dataset/version/export?versionId=${versionId}&fileType=${fileType}`
      let token = getToken()
      axios({
        url,
        method: 'GET',
        responseType: 'blob',
        headers: {
          'access-token': token,
          'client-ip': Vue.ls.get(UserConstant.IP),
          'client-mac': Vue.ls.get(UserConstant.Mac),
          language: getLocale()
        }
      }).then(
        async (response) => {
          if (response.data.size) {
            this.BlobDownLoad(response, fileType)
            console.log(response)
          } else {
            this.$message({
              message: this.$t('未获取到文件'),
              type: 'error'
            })
          }
        },
        (error) => {
          console.log(error)
        }
      )
    },
    BlobDownLoad(res, fileType) {
      const typeMap = {
        1: { mime: 'text/csv', ext: 'csv' },
        2: { mime: 'application/json', ext: 'json' },
        3: { mime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', ext: 'xlsx' },
        4: { mime: 'text/plain', ext: 'txt' },
        5: { mime: 'application/x-zip-compressed', ext: 'zip' }
      }

      // 动态配置
      // 根据后端要求写死为5
      const { mime, ext } = typeMap[5]
      console.log(res, '400')
      let blob = new Blob([res.data], {
        type: mime
      })
      const href = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = href
      a.download = this.$t('导出文件') + `.${ext}`
      a.click()
      a.remove()
      URL.revokeObjectURL(a.href)
    },

    // 格式化 history 字段
    formatHistory(historyStr) {
      try {
        if (!historyStr || historyStr === '[]') {
          return []
        }
        
        const history = JSON.parse(historyStr)
        if (Array.isArray(history)) {
          return history
        }
        
        return []
      } catch (error) {
        console.error('解析 history 字段失败:', error, historyStr)
        return []
      }
    }
  },
  computed: {
    versionIsActive() {
      return this.$refs.leftDatasetVersionTable.tableData.find((item) => item.id === this.currentVersionId)?.isActivity
    },
    rightCols() {
      // 统一使用新的字段结构：instruction、input、output、history
      return [
        // {
        //   prop: 'id',
        //   width: '80px',
        //   label: 'ID'
        // },
        {
          prop: 'instruction', 
          minWidth: '150px',
          label: 'Instruction'
        },
        {
          prop: 'input',
          minWidth: '120px', 
          label: 'Input'
        },
        {
          prop: 'output',
          minWidth: '200px',
          label: 'Output'
        },
        {
          prop: 'history',
          minWidth: '150px',
          label: 'History',
          slotName: 'history'
        },
        {
          label: this.$t('操作'),
          width: '70px',
          prop: 'operation',
          slotName: 'operation'
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.dataset-detail-warapper {
  .dataset-info-topbar {
    height: 30px;
    background-color: white;
    padding: 10px 20px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .name {
      font-weight: 600;
      font-size: 18px;
      margin-right: 10px;
    }

    .type,
    .data-usage {
      background-color: #f0f9e8;
      color: #67c23a;
      border: 1px solid #67c23a;
      padding: 4px;
      border-radius: 5px;
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 10px;
    }

    .data-usage {
      background-color: #fdf6ec;
      color: #ebb563;
      border: 1px solid #ebb563;
    }

    .create-time {
      .label {
        font-size: 14px;
        color: #7d7a7a;
      }

      .time {
        font-size: 14px;
      }
    }
  }
  .content {
    height: calc(100% - 60px);
    display: flex;

    .dataset-tabs-aside {
      width: 350px !important;
      margin-right: 10px;
    }
  }
}

// History 字段样式
.history-content {
  max-width: 300px;
  max-height: 150px;
  overflow-y: auto;
  
  .history-item {
    margin-bottom: 8px;
    padding: 4px 0;
    
    .history-label {
      font-weight: bold;
      color: #409eff;
      margin-right: 4px;
    }
    
    .history-text {
      font-size: 12px;
      color: #606266;
      word-break: break-word;
    }
    
    .history-divider {
      margin: 8px 0 4px 0;
      border: none;
      height: 1px;
      background-color: #e4e7ed;
    }
  }
}

.empty-history {
  color: #c0c4cc;
  font-style: italic;
}
</style>