package com.mediway.platform.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mediway.hos.base.model.BaseResponse;
import com.mediway.platform.model.dto.DataProcessingTaskDTO;
import com.mediway.platform.model.entity.DataProcessingTaskEntity;
import com.mediway.platform.model.vo.DataProcessingTaskVO;
import io.swagger.annotations.Api;

import java.util.List;

/**
 * 数据处理任务服务层接口，定义业务方法
 */
@Api(tags = "数据处理任务管理")
public interface DataProcessingTaskService {

    /**
     * 创建数据处理任务
     * @param dto 数据处理任务 DTO
     */
    void createTask(DataProcessingTaskDTO dto);

    /**
     * 更新数据处理任务
     * @param entity 数据处理任务实体
     * @return 更新是否成功
     */
    boolean updateTask(DataProcessingTaskEntity entity);

    boolean startTask(DataProcessingTaskEntity entity);

    /**
     * 删除数据处理任务（根据 ID）
     * @param id 任务 ID
     * @return 删除是否成功
     */
    boolean deleteTask(String id);

    /**
     * 根据 ID 获取任务详情
     * @param id 任务 ID
     * @return 任务视图对象（DataProcessingTaskVO）
     */
    DataProcessingTaskVO getTaskById(String id);

    /**
     * 获取所有数据处理任务
     * @return 任务视图对象列表
     */
    List<DataProcessingTaskVO> getAllTasks();

    /**
     * 分页查询数据处理任务
     * @param pageNum 当前页码
     * @param pageSize 每页记录数
     * @param taskName 任务名称（可选）
     * @param datasetId 数据集 ID（可选）
     * @param taskStatus 任务状态（可选）
     * @return 分页后的任务视图对象
     */
    IPage<DataProcessingTaskVO> getTaskPage(
            Integer pageNum,
            Integer pageSize,
            String taskName,
            String datasetId,
            String taskStatus);

    /**
     * 批量删除数据处理任务
     * @param ids 任务 ID 列表
     * @return 批量删除是否成功
     */
    boolean batchDeleteTasks(List<String> ids);
}