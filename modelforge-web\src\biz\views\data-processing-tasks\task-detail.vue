<!-- 文件：task-dialog.vue -->
<template>
  <div>
    <!-- 表格 -->
    <div class="dialog-footer">
      <hos-button @click="close">{{ $t('取消') }}</hos-button>
      <hos-button type="success" @click="submit()">{{ $t('保存') }}</hos-button>
    </div>
  </div>
</template>

<script>
import { _debounce } from '@/utils/throttle.js'

export default {
  name: 'TaskDialog',
  props: ['id', 'taskStatusList', 'datasetList'],
  data() {
    return {
      form: {
        taskName: '',
        datasetId: '',
        datasetVersionsIdBefore: '',
        datasetInfoArr: [],
        dataClearCongfig: {
          removeUrl: false,
          filterLength: false,
          lengthStart: 0,
          lengthEnd: 0
        },
        dataPlusCongfig: {
          instrDepSampleNum: 10,
          filterSimThreshold: 0.5,
          genSampleNum: 10,
          promptCfg: ''
        }
      },
      defaultConfig: {
        taskName: '',
        datasetId: '',
        datasetVersionsIdBefore: '',
        datasetInfoArr: [],
        dataClearCongfig: {
          removeUrl: false,
          filterLength: false,
          lengthStart: 0,
          lengthEnd: 0
        },
        dataPlusCongfig: {
          instrDepSampleNum: 10,
          filterSimThreshold: 0.5,
          genSampleNum: 10,
          promptCfg: ''
        }
      },
      datasetProps: {
        value: 'id',
        label: 'name',
        children: 'children'
      },
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            // {
            //   value: 'shejiyuanze',
            //   label: '设计原则'
            // },
            // {
            //   value: 'daohang',
            //   label: '导航'
            // }
          ]
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic'
            },
            {
              value: 'form',
              label: 'Form'
            }
          ]
        }
      ],
      rules: {
        taskName: [{ required: true, message: this.$t('任务名称必填'), trigger: 'blur' }],
        datasetId: [{ required: true, message: this.$t('数据集ID必填'), trigger: 'blur' }]
      }
    }
  },
  async created() {
    if (this.id) {
      const { data } = await this.$api('biz.dataProcessingManage.queryDetailApi', this.id)
      this.form = { ...data }
      this.form.datasetInfoArr = [data.datasetId, data.datasetVersionsIdBefore]
      this.form.dataClearCongfig = {
        removeUrl: JSON.parse(data.dataClearCongfig).removeUrl,
        filterLength: JSON.parse(data.dataClearCongfig).filterLength,
        lengthStart: JSON.parse(data.dataClearCongfig).lengthStart,
        lengthEnd: JSON.parse(data.dataClearCongfig).lengthEnd
      }

      this.form.dataPlusCongfig = {
        instrDepSampleNum: JSON.parse(data.dataPlusCongfig).instrDepSampleNum,
        filterSimThreshold: JSON.parse(data.dataPlusCongfig).filterSimThreshold,
        genSampleNum: JSON.parse(data.dataPlusCongfig).genSampleNum,
        promptCfg: JSON.parse(data.dataPlusCongfig).promptCfg
      }
    } else {
      // 初始化默认值
      this.form = this.defaultConfig
    }
  },
  methods: {
    handleDatasetInfoChange(value) {
      if (value && value.length > 0) {
        this.form.datasetId = value[0]
        this.form.datasetVersionsIdBefore = value[1]
      } else {
        this.form.datasetId = ''
        this.form.datasetVersionsIdBefore = ''
      }
    },
    close() {
      this.$store.commit('CLOSE_DIALOG', { _uid: 'TaskDialog' })
    },
    submit: _debounce(function () {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return

        const payload = {
          ...this.form,
          // 转换JSON配置
          dataClearCongfig: JSON.stringify(this.form.dataClearCongfig),
          dataPlusCongfig: JSON.stringify(this.form.dataPlusCongfig)
        }

        try {
          const apiMethod = this.id ? 'editApi' : 'addApi'
          const { code } = await this.$api(`biz.dataProcessingManage.${apiMethod}`, payload)

          if (code == 200) {
            this.$message.success(this.$t('操作成功'))
            this.$store.commit('UPDATE_TABLE', { _uid: 'taskManageTable' })
            this.close()
          }
        } catch (error) {
          this.$message.error(this.$t('操作失败'))
          console.error('API Error:', error)
        }
      })
    })
  }
}
</script>

<style scoped>
</style>