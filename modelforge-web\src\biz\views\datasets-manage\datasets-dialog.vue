<!-- 文件：dataset-dialog.vue -->
<template>
  <div>
    <hos-form ref="form" :model="form" :rules="rules">
      <hos-form-item :label="$t('数据集名称')" prop="name" label-width="120px">
        <hos-input v-model="form.name" clearable />
      </hos-form-item>

      <!-- <hos-form-item :label="$t('数据集类型')" prop="type" label-width="120px">
        <hos-select v-model="form.type" clearable @change="typeChange">
          <hos-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
        </hos-select>
      </hos-form-item> -->

      <hos-form-item v-show="type == 1" :label="$t('数据用途')" prop="dataUsage" label-width="120px">
        <hos-radio-group v-model="form.dataUsage" style="vertical-align: center">
          <hos-radio v-for="(item, index) in dataUsageList" :key="index" :label="item.value">{{
            item.label
          }}</hos-radio>
        </hos-radio-group>

        <span class="more-tips" @click="showDatauseageTips">说明<i class="hos-icom-tip"></i></span>
      </hos-form-item>

      <hos-form-item :label="$t('数据集描述')" prop="description" label-width="120px">
        <hos-input type="textarea" v-model="form.description" :rows="4" clearable />
      </hos-form-item>

      <!-- <hos-form-item :label="$t('数据文件')" prop="versionFileId" label-width="110px">
        <span v-show="form.versionFileId"
          >{{ form.versionFileId }}
          <tips style="margin-left: 5px" placement="top" :content="$t('文件已成功上传，此为文件id信息。')"
        /></span>
        <file-down-upload
          ref="dataFileUpload"
          :download-template-option="downloadTemplateOption"
          :upload-file-option="uploadFileOption"
          :beforeDownload="beforeDownload"
          :afterUpload="afterUpload"
        ></file-down-upload>
      </hos-form-item> -->
    </hos-form>

    <div slot="footer" class="dialog-footer">
      <hos-button @click="close">{{ $t('取消') }}</hos-button>
      <hos-button type="success" @click="submit">{{ $t('保存') }}</hos-button>
    </div>

    <hos-biz-dialog
      :title="$t('数据用途说明')"
      uid="dataUseageTipsDialog"
      class="set-dialog-custom-height"
      width="70%"
      :close-on-click-modal="false"
    ></hos-biz-dialog>
  </div>
</template>

<script>
import { _debounce } from '@/utils/throttle.js'
import FileDownUpload from './file-down-upload.vue'

export default {
  name: 'DatasetDialog',
  props: ['id', 'typeList', 'dataUsageList', 'type'],
  components: { FileDownUpload },
  data() {
    return {
      form: {
        name: '',
        dataUsage: '',
        description: '',
        storageDirectory: '', // 暂时写死用假数据
        // versionFileId: '',
      },
      rules: {
        name: [{ required: true, message: this.$t('数据集名称必填'), trigger: 'blur' }],
        description: [{ required: true, message: this.$t('数据集描述必填'), trigger: 'blur' }],
        // type: [{ required: true, message: this.$t('类型必选'), trigger: 'change' }],
        // versionFileId: [{ required: true, message: this.$t('数据文件必须上传'), trigger: 'change' }]
      },
      downloadTemplateOption: {
        isHide: false, // 是否隐藏下载
        method: 'GET', // 请求方式
        apiUrl: 'ai/dataset/downloadFile', // 下载模板Api对应的接口地址
        params: {
          type: ''
        }, // 拼到请求路径上的参数
        data: {}, // body参数 POST请求时用到
        templateFileName: this.$t('数据集导入模板')
      },
      uploadFileOption: {
        uploadTypeState: false, // 是否需要选择导入数据类型,目前只有新增场景,后续看接口适配情况
        method: 'POST',
        apiUrl: 'ai/dataset/upload',
        params: {},
        data: {
          accountId: '',
          subjectId: ''
        }
      }
    }
  },
  async created() {
    if (this.type == 2) {
        this.form.dataUsage = ''
      } else {
        this.form.dataUsage = 1
      }

      // 下载模板要用到类型参数
      this.downloadTemplateOption.params.type = this.type
      this.downloadTemplateOption.templateFileName =  this.type == 2 ? this.$t('测评集导入模板') : this.$t('训练集导入模板')

    if (this.id) {
      const { data } = await this.$api('biz.datasetsManage.queryDetailApi', this.id)
      this.form = { ...data }
    }
  },
  methods: {
    // 设置默认值
    // typeChange(val) {
    //   if (val == 2) {
    //     this.form.dataUsage = ''
    //   } else {
    //     this.form.dataUsage = 1
    //   }

    //   // 下载模板要用到类型参数
    //   this.downloadTemplateOption.params.type = val
    //   this.downloadTemplateOption.templateFileName =  val == 2 ? this.$t('测评集导入模板') : this.$t('训练集导入模板')
    // },
    showDatauseageTips() {
      this.$store.commit('OPEN_DIALOG', {
        component: require('./data-useage-tips-dialog.vue').default,
        _uid: 'dataUseageTipsDialog',
        props: {}
      })
    },
    close() {
      this.$store.commit('CLOSE_DIALOG', { _uid: 'DatasetDialog' })
    },
    submit: _debounce(function () {
      this.$refs.form.validate(async (valid) => {
        // if (this.form.name && this.type && !this.form.versionFileId) {
        //   return this.$message.warning(this.$t('请上传数据文件'))
        // }
        if (!valid) return

        const apiName = this.id ? 'editApi' : 'addApi'
        // 手动添加type属性
        this.form.type = this.type
        const { code, msg } = await this.$api(`biz.datasetsManage.${apiName}`, this.form)

        if (code == 200) {
          this.$message.success(msg)
          this.$store.commit('UPDATE_TABLE', { _uid: 'datasetManageTable' })
        }
        this.close()
      })
    }),
    beforeDownload() {
      // 提示先选择数据集类型
      // if (!this.form.type) {
      //   return this.$message.warning(this.$t('请先选择数据集类型'))
      // } else {
      //   this.$refs.dataFileUpload.downloadTemplate()
      // }

      // 现在数据集类型传入
      this.$refs.dataFileUpload.downloadTemplate()
    },
    afterUpload(data) {
      console.log(data, '130')
      this.form.versionFileId = data
    }
  }
}
</script>

<style lang="scss" scoped>
.hos-radio-group {
  vertical-align: middle;
}

.more-tips {
    margin-left:30px;
    color:#509CFC;
    cursor:pointer;
    position: absolute;
    top: 1px;

    i {
      margin-left: 2px;
    }
  }
</style>