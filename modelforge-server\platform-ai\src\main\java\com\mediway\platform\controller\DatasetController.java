package com.mediway.platform.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mediway.hos.base.model.BaseResponse;
import com.mediway.platform.model.dto.DatasetDTO;
import com.mediway.platform.model.dto.FileInfoDTO;
import com.mediway.platform.model.vo.DatasetVO;
import com.mediway.platform.service.DatasetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

/**
 * 数据集管理控制器，处理数据集相关的 HTTP 请求，并处理可能出现的异常
 */
@Api(tags = "数据集管理")
@RestController
@RequestMapping("/ai/dataset")
public class DatasetController {
    private static final Logger logger = LoggerFactory.getLogger(DatasetController.class);

    @Autowired
    private DatasetService datasetService;

    /**
     * 创建数据集
     * @param dto 数据集数据传输对象
     * @return 基础响应对象，包含操作结果信息
     */
    @ApiOperation("创建数据集")
    @PostMapping("/create")
    public BaseResponse createDataset(@Validated @RequestBody DatasetDTO dto) {
        try {
            boolean success = datasetService.createDataset(dto);
            if (success) {
                return BaseResponse.success("数据集创建成功");
            } else {
                logger.error("数据集创建失败，DTO: {}", dto);
                return BaseResponse.error("数据集创建失败");
            }
        } catch (Exception e) {
            logger.error("创建数据集时发生异常", e);
            return BaseResponse.error("创建数据集时发生异常");
        }
    }

    /**
     * 更新数据集
     * @param dto 数据集数据传输对象
     * @return 基础响应对象，包含操作结果信息
     */
    @ApiOperation("更新数据集")
    @PostMapping("/update")
    public BaseResponse updateDataset(@Validated @RequestBody DatasetDTO dto) {
        try {
            boolean success = datasetService.updateDataset(dto);
            if (success) {
                return BaseResponse.success("数据集更新成功");
            } else {
                logger.error("数据集更新失败，DTO: {}", dto);
                return BaseResponse.error("数据集更新失败");
            }
        } catch (Exception e) {
            logger.error("更新数据集时发生异常", e);
            return BaseResponse.error("更新数据集时发生异常");
        }
    }

    /**
     * 删除数据集（逻辑删除，需根据业务需求调整）
     * @param id 数据集的 ID
     * @return 基础响应对象，包含操作结果信息
     */
    @ApiOperation("删除数据集（逻辑删除，需根据业务需求调整）")
    @PostMapping("/delete/{id}")
    public BaseResponse deleteDataset(@PathVariable String id) {
        try {
            boolean success = datasetService.deleteDataset(id);
            if (success) {
                return BaseResponse.success("数据集删除成功");
            } else {
                logger.error("数据集删除失败，ID: {}", id);
                return BaseResponse.error("数据集删除失败");
            }
        } catch (Exception e) {
            logger.error("删除数据集时发生异常，ID: {}", id, e);
            return BaseResponse.error("删除数据集时发生异常");
        }
    }

    /**
     * 获取数据集详情
     * @param id 数据集的 ID
     * @return 基础响应对象，包含操作结果信息和数据集视图对象
     */
    @ApiOperation("获取数据集详情")
    @GetMapping("/detail/{id}")
    public BaseResponse getDatasetById(@PathVariable String id) {
        try {
            DatasetVO vo = datasetService.getDatasetById(id);
            if (vo == null) {
                return BaseResponse.error("数据集不存在");
            }
            return BaseResponse.success(vo);
        } catch (Exception e) {
            logger.error("获取数据集详情时发生异常，ID: {}", id, e);
            return BaseResponse.error("获取数据集详情时发生异常");
        }
    }

    /**
     * 获取所有数据集
     * @param type 数据集类型，可为空
     * @return 基础响应对象，包含操作结果信息和数据集视图对象列表
     */
    @ApiOperation("获取所有数据集")
    @GetMapping("/list")
    public BaseResponse getAllDatasets(@RequestParam(required = false) String type) {
        try {
            List<DatasetVO> vos = datasetService.getAllDatasets(type);
            return BaseResponse.success(vos);
        } catch (Exception e) {
            logger.error("获取所有数据集时发生异常", e);
            return BaseResponse.error("获取数据集列表失败");
        }
    }

    /**
     * 分页查询数据集
     * @param current 当前页码
     * @param size 每页记录数
     * @param name 数据集名称，可为空
     * @param type 数据集类型，可为空
     * @return 基础响应对象，包含操作结果信息和分页的数据集视图对象
     */
    @ApiOperation("分页查询数据集")
    @GetMapping("/page")
    public BaseResponse getDatasetPage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer isActivity,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String dataUsage,
            @RequestParam(required = false) Integer type) {
        try {
            IPage<DatasetVO> voPage = datasetService.getDatasetPage(current, size, name, type, dataUsage, isActivity);
            return BaseResponse.success(voPage);
        } catch (Exception e) {
            logger.error("分页查询数据集时发生异常，参数：pageNum={}, pageSize={}, name={}, type={}",
                    current, size, name, type, e);
            return BaseResponse.error("分页查询数据集失败");
        }
    }

    /**
     * 批量删除数据集
     * @param ids 要删除的数据集 ID 列表
     * @return 基础响应对象，包含操作结果信息
     */
    @ApiOperation("批量删除数据集")
    @PostMapping("/deletion")
    public BaseResponse batchDeleteDatasets(@RequestBody List<String> ids) {
        try {
            boolean success = datasetService.batchDeleteDatasets(ids);
            if (success) {
                return BaseResponse.success();
            } else {
                logger.error("批量删除数据集失败，IDs: {}", ids);
                return BaseResponse.error("批量删除数据集失败");
            }
        } catch (Exception e) {
            logger.error("批量删除数据集时发生异常，IDs: {}", ids, e);
            return BaseResponse.error(e.getMessage());
        }
    }

    /**
     * 获取所有数据集 - 级联选择器
     * @param type 数据集类型，可为空
     * @return 基础响应对象，包含操作结果信息和级联选择器所需的数据结构
     */
    @ApiOperation("获取所有启用的数据集-级联选择器")
    @GetMapping("/list-tree")
    public BaseResponse getSpecificDataFromDB(@RequestParam(required = false) String type) {
        try {
            List<Map<String, Object>> result = datasetService.getSpecificDataFromDB(type);
            return BaseResponse.success(result);
        } catch (Exception e) {
            logger.error("从数据库获取指定数据结构时发生异常", e);
            return BaseResponse.error("从数据库获取指定数据结构失败");
        }
    }


    @ApiOperation("文件上传接口")
    @PostMapping("/upload")
    public BaseResponse<Object> uploadFile(@RequestParam("file") MultipartFile file,
                             @RequestParam("fileName") String fileName,
                             @RequestParam("chunk") int chunk,
                             @RequestParam("totalChunks") int totalChunks) {
        try {
            String result = datasetService.uploadFile(file,fileName,chunk,totalChunks,false, null);
            return BaseResponse.success("上传成功！", result);
        } catch (IOException e) {
            e.printStackTrace();
            return BaseResponse.error("上传失败！");
        }
    }

    @ApiOperation("查看文件接口")
    @GetMapping("/view")
    public BaseResponse<Object> viewFile(@RequestParam(value = "fileId") @ApiParam("文件ID,不可为空") String fileId,
                                         @RequestParam(defaultValue = "1") Integer current,
                                         @RequestParam(defaultValue = "10") Integer size,
                                         @RequestParam(required = false) String search) {
        try {
            Object fileInfo = datasetService.getFileInfo(fileId,current,size,search);
            return BaseResponse.success("获取文件信息成功", fileInfo);
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResponse.error("获取文件信息失败");
        }
    }

    @ApiOperation("下载模板文件")
    @GetMapping("/downloadFile")
    public ResponseEntity<FileSystemResource> downloadFile(@RequestParam Integer type) {
        String path = "files";
        String fileName = type == 1 ? "训练集模板.zip" : "评测集模板.zip";

        File file = new File(path + File.separator + fileName);
        if (!file.exists()) {
            throw new IllegalArgumentException("文件不存在");
        }

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", "attachment; filename=" + file.getName());
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return ResponseEntity.ok()
                .headers(headers)
                .body(new FileSystemResource(file));
    }

    @ApiOperation("数据集版本-导入")
    @PostMapping("/version/import")
    public BaseResponse<Object> versionImport(@RequestParam(value = "versionId") @ApiParam("数据集版本ID") String versionId,
                                              @RequestParam("file") MultipartFile file,
                                              @RequestParam("fileName") String fileName,
                                              @RequestParam("chunk") int chunk,
                                              @RequestParam("totalChunks") int totalChunks) {
        try {
            Boolean success = datasetService.versionImport(file,versionId,fileName,chunk,totalChunks);
            if (success) {
                return BaseResponse.success("数据集版本-导入成功");
            } else {
                logger.error("数据集版本-导入失败，ID: {}", versionId);
                return BaseResponse.error("数据集版本-导入失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResponse.error("数据版本导入失败");
        }
    }


    @ApiOperation("数据集版本-导出")
    @GetMapping("/version/export")
    public ResponseEntity<byte[]> downloadContent(@RequestParam(value = "fileType") @ApiParam("输出文件类型:1csv;2json;3excel;4txt;5xls") Integer fileType,
                                                  @RequestParam(value = "versionId") @ApiParam("数据集版本ID") String versionId) throws IOException {
        // 调用服务层方法获取文件内容和文件名
        FileInfoDTO fileInfo = datasetService.getContentAsFileWithDatasetDb(versionId, fileType);

        if (fileInfo.getContent().length == 0) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        // 获取文件名（可以根据需要动态获取）
        String fileName = fileInfo.getFileName();  // 假设 FileInfoDTO 有一个 getFileName 方法

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", fileName);  // 使用动态获取的文件名

        return new ResponseEntity<>(fileInfo.getContent(), headers, HttpStatus.OK);
    }

}